
import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  SafeAreaView,
  Animated,
  Keyboard,
  Platform,
  Alert,
  FlatList,
  Image
} from 'react-native';
import { Svg, Path, Circle } from 'react-native-svg';
import { useNavigation } from '@react-navigation/native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useSelector } from 'react-redux';
import { examCategoriesAPI, userExamsAPI } from '../services/api';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';

// Define consistent colors
const COLORS = {
  primary: '#3461eb',
  secondary: '#6200ee',
  background: '#F9FAFA',
  card: '#FFFFFF',
  text: '#1C1D22',
  textSecondary: '#3C3F4A',
  border: '#EEEFF2',
  success: '#4CAF50',
  warning: '#FFC107',
  error: '#F44336'
};

// Define consistent fonts
const FONTS = {
  regular: {
    fontFamily: 'System',
    fontWeight: 'normal',
  },
  medium: {
    fontFamily: 'System',
    fontWeight: '500',
  },
  bold: {
    fontFamily: 'System',
    fontWeight: 'bold',
  }
};

const DashboardScreen = () => {
  const navigation = useNavigation();
  const { user, isAuthenticated } = useSelector((state) => state.auth);
  const [searchText, setSearchText] = useState('');
  const [examCategories, setExamCategories] = useState([]);
  const [subscribedExams, setSubscribedExams] = useState([]);
  const [keyboardVisible, setKeyboardVisible] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Animation value for smooth transitions
  const fadeAnim = useState(new Animated.Value(0))[0];

  // Function to fetch exam categories from the backend
  const fetchExamCategories = async () => {
    try {
      console.log('Fetching exam categories...');
      const response = await examCategoriesAPI.getAll();
      console.log('Exam categories fetched:', response);

      // Check if response is an array
      if (Array.isArray(response)) {
        console.log(`Found ${response.length} exam categories`);

        // Log each category for debugging
        if (response.length > 0) {
          response.forEach((category, index) => {
            console.log(`Category ${index + 1}:`, {
              id: category.id,
              name: category.name,
              description: category.description,
              subjects: category.subjects ? `${category.subjects.length} subjects` : 'No subjects'
            });
          });
        }

        setExamCategories(response);
      } else {
        console.log('Invalid response format, setting empty array');
        setExamCategories([]);
      }

      return response;
    } catch (error) {
      console.error('Error fetching exam categories:', error);
      setExamCategories([]);
      setError('Failed to load exam categories. Please try again.');
      return [];
    }
  };

  // Function to fetch user's purchased exams
  const fetchUserExams = async () => {
    try {
      console.log('Fetching exams for user:', user?.id, user?.name);
      const response = await userExamsAPI.getAll();
      console.log('User exams fetched:', response);

      // Check if response is an array
      if (Array.isArray(response) && response.length > 0) {
        // Format the data to match our component's expected structure
        const formattedExams = response.map(exam => {
          // Get the latest attempt if any
          const latestAttempt = exam.attempts && exam.attempts.length > 0
            ? exam.attempts.sort((a, b) => new Date(b.completed_at) - new Date(a.completed_at))[0]
            : null;

          // Get category name from the subject or from the category_id
          let categoryName = '';
          if (exam.subject && exam.subject.category_name) {
            categoryName = exam.subject.category_name;
          } else if (exam.category_name) {
            categoryName = exam.category_name;
          } else if (exam.category_id) {
            // Map category ID to name if needed
            const categoryMap = {
              '1': 'NEET',
              '2': 'JEE',
              '3': 'GATE',
              'NEET': 'NEET',
              'JEE': 'JEE',
              'GATE': 'GATE'
            };
            categoryName = categoryMap[exam.category_id] || exam.category_id;
          }

          // Format the title to include category name for full mock tests
          let title = exam.subject.name;
          if (title.includes('Full Mock Test')) {
            // Title already has the format we want
          } else if (exam.subject_id.toString().startsWith('full_mock_')) {
            // Format as "Category Full Mock Test"
            title = `${categoryName} Full Mock Test`;
          }

          return {
            id: exam.id.toString(),
            title: title,
            description: exam.subject.description || 'Take this test to improve your skills',
            score: latestAttempt ? latestAttempt.score : 0,
            totalQuestions: latestAttempt ? latestAttempt.total_questions : 0,
            attemptCount: exam.attempts ? exam.attempts.length : 0,
            lastAttemptDate: latestAttempt ? latestAttempt.completed_at : exam.purchased_at,
            subjectId: exam.subject_id,
            userExamId: exam.id,
            user_id: exam.user_id,
            category: categoryName,
            categoryName: categoryName,
            category_id: exam.category_id || (exam.subject && exam.subject.category_id)
          };
        });

        console.log(`Found ${formattedExams.length} exams for user ${user?.id}`);
        setSubscribedExams(formattedExams);
        return formattedExams;
      } else {
        console.log('No user exams found or invalid response format');
        setSubscribedExams([]);
        return [];
      }
    } catch (error) {
      console.error('Error fetching user exams:', error);
      // If we can't fetch from API, try to get from local storage as fallback
      try {
        const storedExams = await AsyncStorage.getItem('subscribedExams');
        if (storedExams) {
          const parsedExams = JSON.parse(storedExams);
          setSubscribedExams(parsedExams);
          return parsedExams;
        }
      } catch (storageError) {
        console.error('Error reading from storage:', storageError);
      }

      setSubscribedExams([]);
      setError('Failed to load your purchased exams. Please try again.');
      return [];
    }
  };

  // Function to load data from the backend
  const loadData = async () => {
    setLoading(true);
    setError(null);

    try {
      // Fetch both exam categories and user exams in parallel
      const [categories, userExams] = await Promise.all([
        fetchExamCategories(),
        fetchUserExams()
      ]);

      // Store user exams in AsyncStorage for offline access
      if (userExams.length > 0) {
        await AsyncStorage.setItem('subscribedExams', JSON.stringify(userExams));
      }

      setLoading(false);

      // Fade in animation
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 500,
        useNativeDriver: true
      }).start();
    } catch (error) {
      console.error('Error loading data:', error);
      setError('Failed to load data. Please check your connection and try again.');
      setLoading(false);
    }
  };

  // Initial load
  useEffect(() => {
    // Clear any stored mock data
    const clearMockData = async () => {
      try {
        await AsyncStorage.removeItem('userPurchasedExams');
        await AsyncStorage.removeItem('subscribedExams');
        console.log('Cleared stored mock data');
      } catch (error) {
        console.error('Error clearing stored mock data:', error);
      }
    };

    clearMockData().then(() => loadData());

    // Keyboard listeners for smooth transitions
    const keyboardDidShowListener = Keyboard.addListener(
      Platform.OS === 'ios' ? 'keyboardWillShow' : 'keyboardDidShow',
      () => setKeyboardVisible(true)
    );

    const keyboardDidHideListener = Keyboard.addListener(
      Platform.OS === 'ios' ? 'keyboardWillHide' : 'keyboardDidHide',
      () => setKeyboardVisible(false)
    );

    return () => {
      keyboardDidShowListener.remove();
      keyboardDidHideListener.remove();
    };
  }, []);

  // Reload data when user changes
  useEffect(() => {
    console.log('User changed, reloading data:', user?.id);
    if (isAuthenticated) {
      loadData();
    }
  }, [user?.id, isAuthenticated]);

  // Refresh data when the screen comes into focus
  useEffect(() => {
    const unsubscribe = navigation.addListener('focus', () => {
      console.log('Dashboard screen focused, refreshing data...');
      loadData();
    });

    return unsubscribe;
  }, [navigation]);

  const MagnifyingGlassIcon = () => (
    <Svg xmlns="http://www.w3.org/2000/svg" width={24} height={24} fill="#3C3F4A" viewBox="0 0 256 256">
      <Path d="M229.66,218.34l-50.07-50.06a88.11,88.11,0,1,0-11.31,11.31l50.06,50.07a8,8,0,0,0,11.32-11.32ZM40,112a72,72,0,1,1,72,72A72.08,72.08,0,0,1,40,112Z" />
    </Svg>
  );

  const GraduationCapIcon = () => (
    <Svg xmlns="http://www.w3.org/2000/svg" width={24} height={24} fill="#1C1D22" viewBox="0 0 256 256">
      <Path d="M251.76,88.94l-120-64a8,8,0,0,0-7.52,0l-120,64a8,8,0,0,0,0,14.12L32,117.87v48.42a15.91,15.91,0,0,0,4.06,10.65C49.16,191.53,78.51,216,128,216a130,130,0,0,0,48-8.76V240a8,8,0,0,0,16,0V199.51a115.63,115.63,0,0,0,27.94-22.57A15.91,15.91,0,0,0,224,166.29V117.87l27.76-14.81a8,8,0,0,0,0-14.12ZM128,200c-43.27,0-68.72-21.14-80-33.71V126.4l76.24,40.66a8,8,0,0,0,7.52,0L176,143.47v46.34C163.4,195.69,147.52,200,128,200Zm80-33.75a97.83,97.83,0,0,1-16,14.25V134.93l16-8.53ZM188,118.94l-.22-.13-56-29.87a8,8,0,0,0-7.52,14.12L171,128l-43,22.93L25,96,128,41.07,231,96Z" />
    </Svg>
  );

  const StudentIcon = () => (
    <Svg xmlns="http://www.w3.org/2000/svg" width={24} height={24} fill="#1C1D22" viewBox="0 0 256 256">
      <Path d="M226.53,56.41l-96-32a8,8,0,0,0-5.06,0l-96,32A8,8,0,0,0,24,64v80a8,8,0,0,0,16,0V75.1L73.59,86.29a64,64,0,0,0,20.65,88.05c-18,7.06-33.56,19.83-44.94,37.29a8,8,0,1,0,13.4,8.74C77.77,197.25,101.57,184,128,184s50.23,13.25,65.3,36.37a8,8,0,0,0,13.4-8.74c-11.38-17.46-27-30.23-44.94-37.29a64,64,0,0,0,20.65-88l44.12-14.7a8,8,0,0,0,0-15.18ZM176,120A48,48,0,1,1,89.35,91.55l36.12,12a8,8,0,0,0,5.06,0l36.12-12A47.89,47.89,0,0,1,176,120ZM128,87.57,57.3,64,128,40.43,198.7,64Z" />
    </Svg>
  );

  const HouseIcon = () => (
    <Svg xmlns="http://www.w3.org/2000/svg" width={24} height={24} fill="#1C1D22" viewBox="0 0 256 256">
      <Path d="M224,115.55V208a16,16,0,0,1-16,16H168a16,16,0,0,1-16-16V168a8,8,0,0,0-8-8H112a8,8,0,0,0-8,8v40a16,16,0,0,1-16,16H48a16,16,0,0,1-16-16V115.55a16,16,0,0,1,5.17-11.78l80-75.48.11-.11a16,16,0,0,1,21.53,0,1.14,1.14,0,0,0,.11.11l80,75.48A16,16,0,0,1,224,115.55Z" />
    </Svg>
  );

  const ChartLineIcon = () => (
    <Svg xmlns="http://www.w3.org/2000/svg" width={24} height={24} fill="#3C3F4A" viewBox="0 0 256 256">
      <Path d="M232,208a8,8,0,0,1-8,8H32a8,8,0,0,1-8-8V48a8,8,0,0,1,16,0v94.37L90.73,98a8,8,0,0,1,10.07-.38l58.81,44.11L218.73,90a8,8,0,1,1,10.54,12l-64,56a8,8,0,0,1-10.07.38L96.39,114.29,40,163.63V200H224A8,8,0,0,1,232,208Z" />
    </Svg>
  );

  const UserIcon = () => (
    <Svg xmlns="http://www.w3.org/2000/svg" width={24} height={24} fill="#3C3F4A" viewBox="0 0 256 256">
      <Path d="M230.92,212c-15.23-26.33-38.7-45.21-66.09-54.16a72,72,0,1,0-73.66,0C63.78,166.78,40.31,185.66,25.08,212a8,8,0,1,0,13.85,8c18.84-32.56,52.14-52,89.07-52s70.23,19.44,89.07,52a8,8,0,1,0,13.85-8ZM72,96a56,56,0,1,1,56,56A56.06,56.06,0,0,1,72,96Z" />
    </Svg>
  );

  // New CategoryCard component for grid layout
  const CategoryCard = ({ title, price, rating, reviews, icon, categoryId, onPress, category }) => {
    // Get icon based on category name
    const getIconComponent = () => {
      switch (title.toLowerCase()) {
        case 'neet':
        case 'medicine':
        case 'medical':
          return (
            <View style={[styles.categoryIcon, { backgroundColor: '#FFF9E6' }]}>
              <MaterialCommunityIcons name="stethoscope" size={32} color="#FF9500" />
            </View>
          );
        case 'jee':
        case 'jee mains':
        case 'jee advanced':
        case 'engineering':
        case 'chemistry':
          return (
            <View style={[styles.categoryIcon, { backgroundColor: '#E6F9F0' }]}>
              <MaterialCommunityIcons name="flask" size={32} color="#4CAF50" />
            </View>
          );
        case 'gate':
        case 'biology':
          return (
            <View style={[styles.categoryIcon, { backgroundColor: '#E6F0FF' }]}>
              <MaterialCommunityIcons name="dna" size={32} color="#2196F3" />
            </View>
          );
        case 'cet':
        case 'mathematics':
        case 'math':
          return (
            <View style={[styles.categoryIcon, { backgroundColor: '#FFE6E6' }]}>
              <MaterialCommunityIcons name="chart-pie" size={32} color="#F44336" />
            </View>
          );
        case 'language':
        case 'english':
          return (
            <View style={[styles.categoryIcon, { backgroundColor: '#E6E6FF' }]}>
              <MaterialCommunityIcons name="translate" size={32} color="#673AB7" />
            </View>
          );
        default:
          return (
            <View style={[styles.categoryIcon, { backgroundColor: '#F0F0F0' }]}>
              <MaterialCommunityIcons name="school" size={32} color="#3461eb" />
            </View>
          );
      }
    };

    // Get pricing information from category subjects
    const getPricingInfo = () => {
      if (!category || !category.subjects || category.subjects.length === 0) {
        return { minPrice: 'Free', maxPrice: 'Free', currency: 'INR' };
      }

      const prices = category.subjects
        .filter(subject => subject.price && subject.price > 0)
        .map(subject => subject.price);

      if (prices.length === 0) {
        return { minPrice: 'Free', maxPrice: 'Free', currency: 'INR' };
      }

      const minPrice = Math.min(...prices);
      const maxPrice = Math.max(...prices);
      const currency = category.subjects[0].currency || 'INR';

      if (minPrice === maxPrice) {
        return { minPrice: `₹${minPrice}`, maxPrice: null, currency };
      } else {
        return { minPrice: `₹${minPrice}`, maxPrice: `₹${maxPrice}`, currency };
      }
    };

    const pricingInfo = getPricingInfo();
    const priceDisplay = pricingInfo.maxPrice
      ? `${pricingInfo.minPrice} - ${pricingInfo.maxPrice}`
      : pricingInfo.minPrice;

    return (
      <TouchableOpacity style={styles.categoryCard} onPress={onPress} activeOpacity={0.7}>
        {getIconComponent()}
        <Text style={styles.categoryTitle}>{title}</Text>
        <Text style={styles.categoryPrice}>{priceDisplay}</Text>
        <Text style={styles.categorySubjects}>
          {category && category.subjects ? `${category.subjects.length} subjects` : 'Loading...'}
        </Text>
      </TouchableOpacity>
    );
  };

  const ExamItem = ({ title, description, buttonText, icon, examType, isReview = false, examData = null, categoryId = null }) => {
    // Extract the exam type from the title if not provided
    const extractedExamType = examType || title.split(' ')[0];

    // Check if exam is locked (AI is still generating questions)
    const isLocked = examData && (examData.isLocked || examData.status === 'generating' || examData.status === 'error');

    // Show alert if exam is locked
    const showLockedAlert = () => {
      if (examData && examData.status === 'generating') {
        Alert.alert(
          "Questions Being Generated",
          "Our AI Teacher is still preparing your questions. Please check back later.",
          [{ text: "OK" }]
        );
      } else if (examData && examData.status === 'error') {
        Alert.alert(
          "Error Generating Questions",
          "There was an error generating questions for this exam. Please try again later.",
          [{ text: "OK" }]
        );
      } else {
        Alert.alert(
          "Exam Locked",
          "This exam is currently locked. Please check back later.",
          [{ text: "OK" }]
        );
      }
    };

    const handlePress = () => {
      if (isReview) {
        // Check if exam is locked
        if (isLocked) {
          showLockedAlert();
          return;
        }

        // For subscribed/attempted exams, navigate to exam questions
        // No limit on retakes for purchased tests
        navigation.navigate('ExamQuestionsScreen', {
          subject: title,
          examId: examData.id,
          userExamId: examData.userExamId,
          subjectId: examData.subjectId,
          category: examData.category,
          categoryName: examData.categoryName,
          isRetake: true
        });
      } else {
        // Navigate to QuestionTypeSelectionScreen to show subjects for this category
        console.log('Navigating to QuestionTypeSelectionScreen with category:', categoryId, 'name:', title);
        navigation.navigate('QuestionTypeSelectionScreen', {
          categoryId: categoryId,
          categoryName: title
        });
      }
    };

    // Navigate to achievements screen to view results
    const handleViewResults = () => {
      if (examData && examData.completed) {
        navigation.navigate('Achivements', {
          attendedExam: {
            subject: title,
            score: examData.score,
            totalQuestions: examData.totalQuestions,
            correctAnswers: examData.correctAnswers || examData.score,
            wrongAnswers: examData.wrongAnswers || 0,
            unattempted: examData.unattempted || 0,
            timeTaken: 0 // We don't have this information stored
          }
        });
      }
    };

    // Determine button color based on exam type and locked status
    const getButtonStyle = () => {
      if (!isReview) return [styles.button, { backgroundColor: COLORS.primary }];
      if (isLocked) return [styles.button, { backgroundColor: COLORS.border }];
      return [styles.button, { backgroundColor: COLORS.success }];
    };

    // Get appropriate button text
    const getButtonText = () => {
      if (!isReview) return buttonText;
      return 'Take Test';
    };

    // Format date string
    const formatDate = (dateString) => {
      if (!dateString) return '';
      const date = new Date(dateString);
      return date.toLocaleDateString();
    };

    return (
      <View style={styles.examItem}>
        <TouchableOpacity
          style={styles.examItemLeft}
          onPress={isReview && examData.completed ? handleViewResults : handlePress}
          activeOpacity={0.7}
        >
          <View style={styles.iconContainer}>
            {icon === 'graduation' ? <GraduationCapIcon /> : <StudentIcon />}
            {isLocked && (
              <View style={styles.lockIconOverlay}>
                <MaterialIcons name="lock" size={16} color="#fff" />
              </View>
            )}
          </View>
          <View style={styles.examItemTextContainer}>
            <Text style={styles.examItemTitle} numberOfLines={1}>{title}</Text>
            <Text style={styles.examItemDescription} numberOfLines={2}>
              {isLocked && examData.status === 'generating'
                ? 'AI Teacher is preparing your questions...'
                : isLocked && examData.status === 'error'
                ? 'Error generating questions. Try again later.'
                : description}
            </Text>
            {examData && (
              <View style={styles.scoreContainer}>
                {!isLocked ? (
                  <>
                    <View style={styles.scoreBadge}>
                      <Text style={styles.scoreText}>
                        Score: {examData.score}/{examData.totalQuestions}
                      </Text>
                    </View>
                    <Text style={styles.attemptText}>
                      {examData.lastAttemptDate ? `Last attempt: ${formatDate(examData.lastAttemptDate)}` : `Attempts: ${examData.attemptCount || 0}`}
                    </Text>
                  </>
                ) : (
                  <View style={[styles.scoreBadge, { backgroundColor: COLORS.warning + '20' }]}>
                    <Text style={[styles.scoreText, { color: COLORS.warning }]}>
                      {examData.status === 'generating' ? 'Generating...' : 'Locked'}
                    </Text>
                  </View>
                )}
              </View>
            )}
          </View>
        </TouchableOpacity>
        <View style={styles.buttonContainer}>
          {isReview && examData.completed && !isLocked && (
            <TouchableOpacity
              style={[styles.button, { backgroundColor: COLORS.primary, marginBottom: 8 }]}
              onPress={handleViewResults}
              activeOpacity={0.7}
            >
              <Text style={[styles.buttonText, { color: '#fff' }]}>
                View Results
              </Text>
            </TouchableOpacity>
          )}
          <TouchableOpacity
            style={getButtonStyle()}
            onPress={isLocked && isReview ? showLockedAlert : handlePress}
            activeOpacity={0.7}
          >
            <Text style={[styles.buttonText, { color: isLocked && isReview ? COLORS.textSecondary : '#fff' }]}>
              {isLocked && isReview ? 'Locked' : getButtonText()}
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: COLORS.background }]}>
      <Animated.View style={{ flex: 1, opacity: fadeAnim }}>
        {loading ? (
          <View style={styles.loadingContainer}>
            <Text style={[styles.headerTitle, FONTS.bold]}>Loading...</Text>
          </View>
        ) : error ? (
          <View style={styles.errorContainer}>
            <Text style={[styles.errorText, FONTS.medium]}>{error}</Text>
            <TouchableOpacity
              style={[styles.button, { backgroundColor: COLORS.primary, marginTop: 16 }]}
              onPress={() => {
                setLoading(true);
                setError(null);
                Promise.all([fetchExamCategories(), fetchUserExams()])
                  .then(() => setLoading(false))
                  .catch(err => {
                    console.error('Error refreshing data:', err);
                    setError('Failed to refresh data. Please try again.');
                    setLoading(false);
                  });
              }}
            >
              <Text style={[styles.buttonText, { color: '#fff' }]}>Retry</Text>
            </TouchableOpacity>
          </View>
        ) : (
          <ScrollView
            style={styles.scrollView}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={{ paddingBottom: 20 }}
          >
            <View style={styles.searchContainer}>
              <View style={styles.searchInputContainer}>
                <View style={styles.searchIconContainer}>
                  <MagnifyingGlassIcon />
                </View>
                <TextInput
                  style={[styles.searchInput, FONTS.regular]}
                  placeholder="Search exams"
                  value={searchText}
                  onChangeText={setSearchText}
                  placeholderTextColor={COLORS.textSecondary}
                />
              </View>
            </View>

            {/* 1. Achievements Section */}
            <View style={styles.sectionHeader}>
              <Text style={[styles.sectionTitle, FONTS.bold]}>Your Achievements</Text>
              <TouchableOpacity
                onPress={() => navigation.navigate('Achivements')}
                style={styles.moreButton}
              >
                <Text style={styles.moreButtonText}>View All</Text>
                <MaterialIcons name="chevron-right" size={16} color={COLORS.primary} />
              </TouchableOpacity>
            </View>
            <Text style={styles.sectionDescription}>Track your progress and performance</Text>

            <View style={styles.achievementsContainer}>
              <View style={styles.achievementCard}>
                <View style={[styles.achievementIcon, { backgroundColor: '#E6F9F0' }]}>
                  <MaterialCommunityIcons name="trophy" size={24} color="#4CAF50" />
                </View>
                <Text style={styles.achievementValue}>{subscribedExams.length}</Text>
                <Text style={styles.achievementLabel}>Tests Taken</Text>
              </View>

              <View style={styles.achievementCard}>
                <View style={[styles.achievementIcon, { backgroundColor: '#FFF9E6' }]}>
                  <MaterialCommunityIcons name="star" size={24} color="#FFC107" />
                </View>
                <Text style={styles.achievementValue}>
                  {subscribedExams.length > 0
                    ? Math.round(subscribedExams.reduce((sum, exam) => sum + (exam.score || 0), 0) / subscribedExams.length) + '%'
                    : '0%'}
                </Text>
                <Text style={styles.achievementLabel}>Avg. Score</Text>
              </View>
            </View>

            <View style={styles.achievementsContainer}>
              <View style={styles.achievementCard}>
                <View style={[styles.achievementIcon, { backgroundColor: '#E6F0FF' }]}>
                  <MaterialCommunityIcons name="check-circle-outline" size={24} color="#2196F3" />
                </View>
                <Text style={styles.achievementValue}>{subscribedExams.filter(exam => exam.completed).length}</Text>
                <Text style={styles.achievementLabel}>Completed</Text>
              </View>

              <View style={styles.achievementCard}>
                <View style={[styles.achievementIcon, { backgroundColor: '#FFE6EC' }]}>
                  <MaterialCommunityIcons name="progress-clock" size={24} color="#FF4081" />
                </View>
                <Text style={styles.achievementValue}>
                  {subscribedExams.filter(exam =>
                    !exam.completed ||
                    (exam.attemptCount && exam.attemptCount < (exam.max_retakes || 3))
                  ).length}
                </Text>
                <Text style={styles.achievementLabel}>In Progress</Text>
              </View>
            </View>

            <View style={styles.divider} />

            {/* 2. Recent Purchases Section */}
            <View style={styles.sectionHeader}>
              <Text style={[styles.sectionPurchase, FONTS.bold]}>
                {user?.name ? `${user.name}'s Recent Purchases` : 'Your Recent Purchases'}
              </Text>
              {subscribedExams.length > 3 && (
                <TouchableOpacity
                  onPress={() => navigation.navigate('Achivements')}
                  style={styles.moreButton}
                >
                  <Text style={styles.moreButtonText}>More</Text>
                  <MaterialIcons name="chevron-right" size={16} color={COLORS.primary} />
                </TouchableOpacity>
              )}
            </View>
            <Text style={styles.sectionDescription}>Take unlimited attempts of your purchased tests</Text>

            {subscribedExams.length > 0 ? (
              <>
                {subscribedExams.slice(0, 3).map((exam) => (
                  <ExamItem
                    key={exam.id}
                    title={exam.title}
                    description={exam.description}
                    buttonText="Review"
                    icon="student"
                    isReview={true}
                    examData={exam}
                  />
                ))}
                {subscribedExams.length > 3 && (
                  <TouchableOpacity
                    style={styles.viewAllButton}
                    onPress={() => navigation.navigate('Achivements')}
                  >
                    <Text style={styles.viewAllText}>View All Purchased Tests</Text>
                  </TouchableOpacity>
                )}
              </>
            ) : (
              <View style={styles.emptyStateContainer}>
                <MaterialIcons name="shopping-cart" size={48} color={COLORS.lightGray || '#D1D1D1'} />
                <Text style={[styles.emptyStateText, FONTS.medium]}>
                  {user?.name ? `${user.name}, you haven't purchased any tests yet` : `You haven't purchased any tests yet`}
                </Text>
                <Text style={styles.emptyStateSubText}>
                  Explore our exam categories below and purchase tests to start practicing and improving your skills!
                </Text>
              </View>
            )}

            <View style={styles.divider} />

            {/* 3. All Exam Categories Section */}
            <Text style={[styles.sectionTitle, FONTS.bold]}>All Exam Categories</Text>
            <Text style={styles.sectionDescription}>Select a category to explore subjects and mock tests</Text>

            {examCategories.length > 0 ? (
              <View style={styles.categoryGrid}>
                {examCategories.map((category) => (
                  <CategoryCard
                    key={category.id}
                    title={category.name}
                    category={category}
                    icon={category.icon}
                    categoryId={category.id}
                    onPress={() => {
                      // Navigate to FullExamList (the correct registered name) with the selected category
                      navigation.navigate('FullExamList', {
                        selectedCategoryId: category.id,
                        selectedCategoryName: category.name
                      });
                    }}
                  />
                ))}
              </View>
            ) : (
              <View style={styles.emptyStateContainer}>
                <Text style={[styles.emptyStateText, FONTS.medium]}>
                  No exam categories available.
                </Text>
              </View>
            )}
          </ScrollView>
        )}
      </Animated.View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  scrollView: {
    flex: 1,
  },
  // Achievements section styles
  achievementsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    marginBottom: 12,
  },
  achievementCard: {
    width: '48%',
    backgroundColor: COLORS.card,
    borderRadius: 12,
    padding: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
    alignItems: 'center',
  },
  achievementIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  achievementValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: COLORS.text,
    marginBottom: 4,
  },
  achievementLabel: {
    fontSize: 12,
    color: COLORS.textSecondary,
    textAlign: 'center',
  },

  // Category grid styles
  categoryGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    marginBottom: 16,
  },
  categoryCard: {
    width: '48%',
    backgroundColor: COLORS.card,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
    alignItems: 'center',
  },
  categoryIcon: {
    width: 64,
    height: 64,
    borderRadius: 32,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
  },
  categoryTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.text,
    marginBottom: 4,
    textAlign: 'center',
  },
  categoryPrice: {
    fontSize: 14,
    fontWeight: 'bold',
    color: COLORS.primary,
    marginBottom: 4,
    textAlign: 'center',
  },
  categorySubjects: {
    fontSize: 12,
    color: COLORS.textSecondary,
    marginBottom: 8,
    textAlign: 'center',
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  ratingText: {
    fontSize: 12,
    fontWeight: '500',
    color: COLORS.text,
    marginLeft: 4,
  },
  reviewsText: {
    fontSize: 12,
    color: COLORS.textSecondary,
    marginLeft: 4,
  },
  header: {
    backgroundColor: COLORS.background,
    padding: 16,
    paddingBottom: 8,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  headerTitle: {
    color: COLORS.text,
    fontSize: 18,
    flex: 1,
    textAlign: 'center',
    letterSpacing: -0.4,
  },
  searchContainer: {
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  searchInputContainer: {
    flexDirection: 'row',
    height: 48,
    width: '100%',
    borderRadius: 12,
    backgroundColor: COLORS.border,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  searchIconContainer: {
    paddingLeft: 16,
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  searchInput: {
    flex: 1,
    height: '100%',
    color: COLORS.text,
    paddingHorizontal: 8,
    fontSize: 16,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingTop: 20,
  },
  sectionTitle: {
    fontSize: 22,
    color: COLORS.text,
    paddingBottom: 4,
    paddingHorizontal: 16,
    letterSpacing: -0.3,
  },
  sectionPurchase:{
    fontSize: 22,
    color: COLORS.text,
    paddingBottom: 4,
    letterSpacing: -0.3,
  },
  moreButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 4,
  },
  moreButtonText: {
    fontSize: 14,
    color: COLORS.primary,
    fontWeight: '500',
    marginRight: 2,
  },
  sectionDescription: {
    fontSize: 14,
    color: COLORS.textSecondary,
    paddingHorizontal: 16,
    paddingBottom: 12,
    letterSpacing: -0.3,
  },
  examItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    minHeight: 72,
    paddingVertical: 12,
    backgroundColor: COLORS.card,
    marginHorizontal: 16,
    marginBottom: 12,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  examItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
    flex: 1,
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 8,
    backgroundColor: COLORS.border,
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
  },
  lockIconOverlay: {
    position: 'absolute',
    bottom: -5,
    right: -5,
    backgroundColor: COLORS.warning,
    borderRadius: 10,
    width: 20,
    height: 20,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: '#fff',
  },
  examItemTextContainer: {
    justifyContent: 'center',
    flex: 1,
  },
  examItemTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: COLORS.text,
    marginBottom: 4,
  },
  examItemDescription: {
    fontSize: 14,
    color: COLORS.textSecondary,
    marginBottom: 2,
  },
  scoreContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginTop: 4,
  },
  scoreBadge: {
    backgroundColor: COLORS.primary + '20', // 20% opacity
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 4,
  },
  scoreText: {
    fontSize: 12,
    color: COLORS.primary,
    fontWeight: '500',
  },
  attemptText: {
    fontSize: 12,
    color: COLORS.textSecondary,
    fontWeight: '400',
  },
  buttonContainer: {
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    minWidth: 84,
  },
  button: {
    backgroundColor: COLORS.border,
    minWidth: 84,
    paddingHorizontal: 16,
    height: 36,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
  },
  buttonText: {
    color: COLORS.text,
    fontSize: 14,
    fontWeight: '500',
  },
  divider: {
    height: 1,
    backgroundColor: COLORS.border,
    marginHorizontal: 16,
    marginVertical: 16,
  },
  emptyStateContainer: {
    padding: 24,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: COLORS.card,
    marginHorizontal: 16,
    borderRadius: 12,
    marginTop: 8,
  },
  emptyStateText: {
    fontSize: 18,
    color: COLORS.textSecondary,
    textAlign: 'center',
    marginTop: 16,
    marginBottom: 8,
    fontWeight: '500',
  },
  emptyStateSubText: {
    fontSize: 14,
    color: COLORS.textSecondary,
    textAlign: 'center',
    paddingHorizontal: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  errorText: {
    fontSize: 16,
    color: COLORS.error,
    textAlign: 'center',
    marginBottom: 16,
  },
  footer: {
    flexDirection: 'row',
    borderTopWidth: 1,
    borderColor: COLORS.border,
    backgroundColor: COLORS.card,
    paddingHorizontal: 16,
    paddingTop: 8,
    paddingBottom: 12,
  },
  footerTab: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'flex-end',
    gap: 4,
  },
  footerIconContainer: {
    height: 32,
    alignItems: 'center',
    justifyContent: 'center',
  },
  footerTabText: {
    fontSize: 12,
    fontWeight: '500',
    color: COLORS.textSecondary,
    letterSpacing: 0.2,
  },
  footerTabTextActive: {
    fontSize: 12,
    fontWeight: '500',
    color: COLORS.text,
    letterSpacing: 0.2,
  },
  viewAllButton: {
    backgroundColor: COLORS.background,
    borderWidth: 1,
    borderColor: COLORS.border,
    borderRadius: 12,
    padding: 12,
    marginHorizontal: 16,
    marginVertical: 8,
    alignItems: 'center',
  },
  viewAllText: {
    color: COLORS.primary,
    fontSize: 14,
    fontWeight: '500',
  },
});

export default DashboardScreen;
