from flask import request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from app.ai import ai_bp
from app.models.exam import Subject
import os
import shutil
import json
from werkzeug.utils import secure_filename

# Try to import ollama
try:
    import ollama
    OLLAMA_AVAILABLE = True
except ImportError:
    print("Warning: ollama not available. MCQ generation will not work.")
    OLLAMA_AVAILABLE = False

# Try to import PyPDF2
try:
    from PyPDF2 import PdfReader
    PYPDF2_AVAILABLE = True
except ImportError:
    print("Warning: PyPDF2 not available. PDF processing will not work.")
    PYPDF2_AVAILABLE = False

# Try to import sentence transformers
try:
    from sentence_transformers import SentenceTransformer
    embedding_model = SentenceTransformer('all-MiniLM-L6-v2')
    SENTENCE_TRANSFORMERS_AVAILABLE = True
except ImportError:
    print("Warning: sentence_transformers not available. Some features will be limited.")
    embedding_model = None
    SENTENCE_TRANSFORMERS_AVAILABLE = False

# # Create a directory for storing uploaded PDFs
# UPLOAD_FOLDER = os.path.join(os.getcwd(), 'uploads')
# if not os.path.exists(UPLOAD_FOLDER):
#     os.makedirs(UPLOAD_FOLDER)

# @ai_bp.route('/process-pdf', methods=['POST'])
# def process_pdf():
#     """Process a single PDF file and create embeddings"""
#     if 'file' not in request.files:
#         return jsonify({'error': 'No file provided'}), 400

#     file = request.files['file']
#     if file.filename == '':
#         return jsonify({'error': 'No file selected'}), 400

#     if not file.filename.endswith('.pdf'):
#         return jsonify({'error': 'File must be a PDF'}), 400

#     # Save the file temporarily
#     temp_path = f"temp_{secure_filename(file.filename)}"
#     file.save(temp_path)

#     try:
#         num_chunks = ai_service.process_pdf(temp_path)
#         return jsonify({
#             'message': 'PDF processed successfully',
#             'chunks': num_chunks
#         })
#     finally:
#         # Clean up temporary file
#         if os.path.exists(temp_path):
#             os.remove(temp_path)

# @ai_bp.route('/process-pdfs', methods=['POST'])
# def process_pdfs():
#     """Process multiple PDF files and create embeddings"""
#     if 'files[]' not in request.files:
#         return jsonify({'error': 'No files provided'}), 400

#     files = request.files.getlist('files[]')
#     if not files or len(files) == 0:
#         return jsonify({'error': 'No files selected'}), 400

#     # Create a temporary directory for the uploaded files
#     temp_dir = os.path.join(UPLOAD_FOLDER, 'temp_batch')
#     if os.path.exists(temp_dir):
#         shutil.rmtree(temp_dir)
#     os.makedirs(temp_dir)

#     try:
#         # Save all files to the temporary directory
#         for file in files:
#             if file.filename == '':
#                 continue

#             if not file.filename.endswith('.pdf'):
#                 continue

#             file_path = os.path.join(temp_dir, secure_filename(file.filename))
#             file.save(file_path)

#         # Process all PDFs in the directory
#         num_chunks = ai_service.process_pdf_directory(temp_dir)

#         return jsonify({
#             'message': f'Successfully processed {len(files)} PDF files',
#             'chunks': num_chunks
#         })
#     except Exception as e:
#         return jsonify({'error': str(e)}), 500
#     finally:
#         # Clean up temporary directory
#         if os.path.exists(temp_dir):
#             shutil.rmtree(temp_dir)

@ai_bp.route('/chat', methods=['POST'])
def chat():
    """Get a response from the chatbot using Ollama"""
    data = request.get_json() or {}

    if 'question' not in data:
        return jsonify({'error': 'Question is required'}), 400

    if not OLLAMA_AVAILABLE:
        return jsonify({
            'error': 'Ollama is not available. Please install the ollama package.'
        }), 500

    try:
        # Get chat history from request
        chat_history = data.get('chat_history', [])
        question = data['question']

        # Get user ID if available
        user_id = None
        if request.headers.get('Authorization'):
            try:
                user_id = get_jwt_identity()
            except:
                # Continue without user ID
                pass

        # Generate response using Ollama
        response_text = generate_chat_response_ollama(question, chat_history)

        # Create response object
        response_data = {
            'response': response_text,
            'timestamp': data.get('timestamp', None),
            'model': 'llama3.2:1b'
        }

        return jsonify(response_data)
    except ValueError as e:
        return jsonify({'error': str(e)}), 400
    except Exception as e:
        print(f"Error in chat endpoint: {e}")
        return jsonify({'error': f'An error occurred: {str(e)}'}), 500

# @ai_bp.route('/generate-mcq', methods=['POST'])
# @jwt_required()
# def generate_mcq():
#     """Generate MCQ questions for a given topic and store them"""
#     data = request.get_json() or {}

#     if not all(k in data for k in ('topic', 'subject_id')):
#         return jsonify({'error': 'Topic and subject_id are required'}), 400

#     try:
#         user_id = get_jwt_identity()
#         num_questions = data.get('num_questions', 5)

#         # Check if subject exists
#         subject = Subject.query.get(data['subject_id'])
#         if not subject:
#             return jsonify({'error': 'Invalid subject ID'}), 404

#         # Generate and store questions
#         questions = ai_service.generate_and_store_mcq(
#             topic=data['topic'],
#             subject_id=data['subject_id'],
#             user_id=user_id,
#             num_questions=num_questions
#         )

#         return jsonify({
#             'message': f'Successfully generated {len(questions)} questions',
#             'questions': [q.to_dict() for q in questions]
#         })

#     except ValueError as e:
#         return jsonify({'error': str(e)}), 400
#     except Exception as e:
#         print(f"Error generating MCQ questions: {e}")
#         return jsonify({'error': f'Failed to generate questions: {str(e)}'}), 500

# @ai_bp.route('/generate-mcq-preview', methods=['POST'])
# def generate_mcq_preview():
#     """Generate MCQ questions for preview without storing them"""
#     data = request.get_json() or {}

#     if 'topic' not in data:
#         return jsonify({'error': 'Topic is required'}), 400

#     try:
#         num_questions = data.get('num_questions', 3)
#         subject_name = data.get('subject_name', 'General')

#         # Generate MCQs without storing them
#         if ai_service.vector_store:
#             mcq_json = ai_service.generate_mcq(data['topic'], num_questions)
#         else:
#             mcq_json = ai_service.generate_general_mcq(data['topic'], subject_name, num_questions)

#         # Parse the response as JSON
#         questions_data = json.loads(mcq_json)

#         return jsonify({
#             'message': f'Successfully generated {len(questions_data)} questions',
#             'questions': questions_data
#         })
#     except json.JSONDecodeError as e:
#         return jsonify({'error': f'Failed to parse AI response as JSON: {str(e)}'}), 400
#     except Exception as e:
#         print(f"Error generating MCQ preview: {e}")
#         return jsonify({'error': f'Failed to generate questions: {str(e)}'}), 500

# PDF folder configuration
PDF_FOLDER = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), 'pdfs')

# Global variables for storing processed data
texts = []
sources = []
embeddings = []

# Helper functions for PDF processing and MCQ generation
def extract_texts_from_pdfs(folder_path):
    """Extract text from all PDF files in the given folder"""
    if not PYPDF2_AVAILABLE:
        print("Cannot extract text from PDFs: PyPDF2 not available")
        return []

    # Check if the folder exists
    if not os.path.exists(folder_path):
        print(f"Warning: PDF folder '{folder_path}' does not exist. Creating it.")
        os.makedirs(folder_path, exist_ok=True)
        return []

    all_text = []
    for filename in os.listdir(folder_path):
        if filename.endswith('.pdf'):
            pdf_path = os.path.join(folder_path, filename)
            try:
                reader = PdfReader(pdf_path)
                text = ''
                for page in reader.pages:
                    text += page.extract_text() or ''
                all_text.append((text, filename))
            except Exception as e:
                print(f"Error reading PDF {filename}: {e}")
                continue
    return all_text

def create_embeddings(text_data):
    """Create embeddings for text data"""
    texts = [item[0] for item in text_data]
    sources = [item[1] for item in text_data]

    if not SENTENCE_TRANSFORMERS_AVAILABLE:
        print("Cannot create embeddings: sentence_transformers not available")
        return None, texts, sources

    try:
        text_embeddings = embedding_model.encode(texts, convert_to_tensor=False)
        return text_embeddings, texts, sources
    except Exception as e:
        print(f"Error creating embeddings: {e}")
        return None, texts, sources

def load_or_create_index():
    """Load or create embeddings from PDF files"""
    data = extract_texts_from_pdfs(PDF_FOLDER)
    if not data:
        return None, [], []
    text_embeddings, texts, sources = create_embeddings(data)
    return text_embeddings, texts, sources

def get_relevant_pdf_content(question, max_chunks=3):
    """Retrieve relevant content from PDFs based on the question"""
    global texts, sources, embeddings

    # Load PDF data if not already loaded
    if not texts:
        embeddings, texts, sources = load_or_create_index()

    # If no PDFs available, return empty context
    if not texts:
        return "Reference Material: No PDF documents are currently available for reference."

    # Simple keyword-based matching when embeddings are not available
    if not SENTENCE_TRANSFORMERS_AVAILABLE:
        return get_relevant_content_by_keywords(question, texts, sources, max_chunks)

    try:
        # Create embedding for the question
        question_embedding = embedding_model.encode([question], convert_to_tensor=False)[0]

        # Calculate similarity scores
        import numpy as np
        similarities = []
        for text_embedding in embeddings:
            # Calculate cosine similarity
            similarity = np.dot(question_embedding, text_embedding) / (
                np.linalg.norm(question_embedding) * np.linalg.norm(text_embedding)
            )
            similarities.append(similarity)

        # Get indices of most similar texts
        top_indices = np.argsort(similarities)[-max_chunks:][::-1]

        # Build context from most relevant texts
        context = "Reference Material from PDF documents:\n\n"
        for idx in top_indices:
            if similarities[idx] > 0.1:  # Only include if similarity is above threshold
                # Take relevant portion of text (first 800 characters)
                text_snippet = texts[idx][:800] + "..." if len(texts[idx]) > 800 else texts[idx]
                context += f"Source: {sources[idx]} (Relevance: {similarities[idx]:.2f})\n{text_snippet}\n\n"

        if context == "Reference Material from PDF documents:\n\n":
            context = "Reference Material: No highly relevant content found in PDF documents for this specific question."

        return context

    except Exception as e:
        print(f"Error retrieving PDF content: {e}")
        return get_relevant_content_by_keywords(question, texts, sources, max_chunks)

def get_relevant_content_by_keywords(question, texts, sources, max_chunks=3):
    """Simple keyword-based content retrieval when embeddings are not available"""
    question_lower = question.lower()

    # Extract keywords from the question
    keywords = []
    # Physics keywords
    if any(word in question_lower for word in ['physics', 'force', 'motion', 'energy', 'wave', 'light', 'electricity', 'magnetism', 'thermodynamics']):
        keywords.extend(['physics', 'force', 'motion', 'energy', 'wave', 'light', 'electricity', 'magnetism', 'thermodynamics'])

    # Biology keywords
    if any(word in question_lower for word in ['biology', 'cell', 'photosynthesis', 'respiration', 'genetics', 'evolution', 'anatomy']):
        keywords.extend(['biology', 'cell', 'photosynthesis', 'respiration', 'genetics', 'evolution', 'anatomy'])

    # Chemistry keywords
    if any(word in question_lower for word in ['chemistry', 'atom', 'molecule', 'reaction', 'organic', 'inorganic', 'periodic']):
        keywords.extend(['chemistry', 'atom', 'molecule', 'reaction', 'organic', 'inorganic', 'periodic'])

    # Math keywords
    if any(word in question_lower for word in ['math', 'calculus', 'algebra', 'geometry', 'trigonometry', 'equation']):
        keywords.extend(['math', 'calculus', 'algebra', 'geometry', 'trigonometry', 'equation'])

    # Score texts based on keyword matches
    scored_texts = []
    for i, text in enumerate(texts):
        text_lower = text.lower()
        score = 0

        # Count keyword matches
        for keyword in keywords:
            score += text_lower.count(keyword)

        # Also check for direct question words
        question_words = [word for word in question_lower.split() if len(word) > 3]
        for word in question_words:
            score += text_lower.count(word) * 0.5

        if score > 0:
            scored_texts.append((score, i, text, sources[i]))

    # Sort by score and take top results
    scored_texts.sort(reverse=True, key=lambda x: x[0])
    top_texts = scored_texts[:max_chunks]

    if not top_texts:
        # If no keyword matches, return first few PDFs
        context = "Reference Material from PDF documents (general content):\n\n"
        for i in range(min(max_chunks, len(texts))):
            text_snippet = texts[i][:600] + "..." if len(texts[i]) > 600 else texts[i]
            context += f"Source: {sources[i]}\n{text_snippet}\n\n"
        return context

    # Build context from relevant texts
    context = "Reference Material from PDF documents:\n\n"
    for score, idx, text, source in top_texts:
        # Take relevant portion of text (first 800 characters)
        text_snippet = text[:800] + "..." if len(text) > 800 else text
        context += f"Source: {source} (Keyword matches: {int(score)})\n{text_snippet}\n\n"

    return context

def generate_mcq_from_combined_content(all_texts, sources, max_content_length=8000, num_questions=5, topic=None, subject=None):
    """Generate MCQ using Ollama from combined content"""
    if not OLLAMA_AVAILABLE:
        print("Cannot generate MCQs: ollama not available")
        return "MCQ generation not available. Please install ollama package.", []

    # Combine content from multiple PDFs, keeping track of sources
    combined_content = ""
    used_sources = []

    for i, (text, source) in enumerate(zip(all_texts, sources)):
        if len(combined_content) + len(text[:1000]) < max_content_length:
            combined_content += f"\n--- From {source} ---\n{text[:1000]}\n"
            used_sources.append(source)
        else:
            break

    # Create a more specific prompt based on topic and subject
    topic_context = f" about {topic}" if topic else ""
    subject_context = f" for {subject} subject" if subject else ""

    prompt = f"""Based on the following content from multiple educational documents, generate {num_questions} comprehensive multiple-choice questions{topic_context}{subject_context}.

Make sure the questions cover different topics and difficulty levels from the provided content.

Content from multiple sources:
\"\"\"
{combined_content}
\"\"\"

Format the response as a valid JSON array where each question object has these exact fields:
[
    {{
        "question": "Question text here",
        "options": ["A. Option A text", "B. Option B text", "C. Option C text", "D. Option D text"],
        "correct_answer": "A",
        "explanation": "Detailed explanation of why this answer is correct",
        "difficulty": "medium"
    }},
    ...
]

Requirements:
- Each question must have exactly 4 options labeled A, B, C, D
- The correct_answer must be one of: A, B, C, or D
- Difficulty must be one of: easy, medium, hard
- Ensure the JSON is properly formatted and valid
- Do not include any text before or after the JSON array

Sources used: {', '.join(used_sources)}
"""

    try:
        response = ollama.chat(model='llama3.2:1b', messages=[{'role': 'user', 'content': prompt}])
        return response['message']['content'], used_sources
    except Exception as e:
        print(f"Error generating MCQ with Ollama: {e}")
        return f"Error generating MCQ: {str(e)}", used_sources

def generate_chat_response_ollama(question, chat_history=[]):
    """Generate a chat response using Ollama with PDF content retrieval"""
    # First, try to get relevant content from PDFs
    pdf_context = get_relevant_pdf_content(question)

    if not OLLAMA_AVAILABLE:
        # Fallback to intelligent responses when Ollama is not available
        return generate_intelligent_fallback_response_with_pdf(question, chat_history, pdf_context)

    # Format chat history for context
    conversation_context = ""
    if chat_history:
        for entry in chat_history[-5:]:  # Use last 5 messages for context
            role = entry.get('role', 'user')
            content = entry.get('content', '')
            if role == 'user':
                conversation_context += f"Student: {content}\n"
            else:
                conversation_context += f"AI Master: {content}\n"

    # Create a comprehensive prompt that includes PDF content
    prompt = f"""You are an AI Master, an expert educational assistant specializing in exam preparation for competitive exams like NEET, JEE, and other academic subjects. You are helpful, knowledgeable, and encouraging.

Previous conversation:
{conversation_context}

Current question: {question}

{pdf_context}

Please provide a helpful, accurate, and educational response. If the question is about:
- Study techniques: Provide practical study methods and tips
- Subject concepts: Explain clearly with examples when possible
- Exam preparation: Give specific strategies and advice
- Practice questions: Help solve or explain the approach
- Motivation: Provide encouraging and supportive guidance

IMPORTANT: If relevant information is provided in the reference material above, use it to enhance your response. Always prioritize accuracy and cite the source when using specific information from the reference material.

Keep your response conversational, helpful, and focused on education. If you're not sure about something, it's okay to say so and suggest where the student might find more information.

Response:"""

    try:
        response = ollama.chat(model='llama3.2:1b', messages=[{'role': 'user', 'content': prompt}])
        return response['message']['content']
    except Exception as e:
        print(f"Error generating chat response with Ollama: {e}")
        return generate_intelligent_fallback_response_with_pdf(question, chat_history, pdf_context)

def generate_intelligent_fallback_response_with_pdf(question, chat_history=[], pdf_context=""):
    """Generate intelligent educational responses with PDF context when Ollama is not available"""
    # First try to extract useful information from PDF context
    pdf_info = ""
    if pdf_context and "Reference Material:" in pdf_context and "No PDF documents" not in pdf_context:
        pdf_info = f"\n\nBased on the reference materials available, I can provide some additional context to help answer your question."

    # Get the base response
    base_response = generate_intelligent_fallback_response(question, chat_history)

    # Enhance with PDF context if available
    if pdf_info:
        enhanced_response = base_response + pdf_info + "\n\nFor more detailed information, please refer to the study materials provided."
        return enhanced_response

    return base_response

def generate_intelligent_fallback_response(question, chat_history=[]):
    """Generate intelligent educational responses when Ollama is not available"""
    question_lower = question.lower()

    # Analyze chat history for context
    context_topics = []
    if chat_history:
        for entry in chat_history[-3:]:  # Look at last 3 messages
            content = entry.get('content', '').lower()
            if 'physics' in content:
                context_topics.append('physics')
            elif 'chemistry' in content:
                context_topics.append('chemistry')
            elif 'biology' in content:
                context_topics.append('biology')
            elif 'math' in content:
                context_topics.append('mathematics')

    # Study techniques and methods
    if any(word in question_lower for word in ['study', 'prepare', 'preparation', 'technique', 'method', 'how to']):
        if 'neet' in question_lower:
            return """For NEET preparation, here are some effective study techniques:

1. **NCERT Foundation**: Start with NCERT books thoroughly - they form 60-70% of NEET questions
2. **Active Recall**: Test yourself regularly instead of just re-reading
3. **Spaced Repetition**: Review topics at increasing intervals
4. **Practice Tests**: Take mock tests weekly to track progress
5. **Time Management**: Allocate time based on weightage - Biology (50%), Physics (25%), Chemistry (25%)

Focus on understanding concepts rather than memorizing. Biology requires more memorization, while Physics and Chemistry need conceptual clarity. Would you like specific tips for any particular subject?"""

        elif 'jee' in question_lower:
            return """For JEE preparation, follow these proven study techniques:

1. **Conceptual Understanding**: Focus on understanding 'why' behind formulas
2. **Problem-Solving Practice**: Solve 10-15 problems daily per subject
3. **Formula Sheets**: Create and regularly review formula compilations
4. **Previous Year Analysis**: Solve last 10 years' papers to understand patterns
5. **Weak Area Focus**: Spend 60% time on weak topics, 40% on strong ones

Mathematics requires extensive practice, Physics needs conceptual clarity with numerical skills, and Chemistry needs both theory and numerical practice. Which subject would you like specific guidance on?"""

        else:
            return """Here are some universal study techniques for exam preparation:

1. **Pomodoro Technique**: Study for 25 minutes, then take a 5-minute break
2. **Active Learning**: Summarize, teach others, create mind maps
3. **Practice Testing**: Regular self-assessment is more effective than re-reading
4. **Distributed Practice**: Spread study sessions over time rather than cramming
5. **Interleaving**: Mix different topics in one study session

Remember, consistency beats intensity. Study 4-6 hours daily with focus rather than 10 hours with distractions. What specific subject or topic would you like help with?"""

    # Subject-specific questions
    elif 'physics' in question_lower or 'physics' in context_topics:
        if any(word in question_lower for word in ['difficult', 'hard', 'struggle', 'problem']):
            return """Physics can be challenging, but here's how to tackle it effectively:

1. **Start with Basics**: Ensure you understand fundamental concepts before moving to complex problems
2. **Visualization**: Draw diagrams for mechanics, circuits, and wave problems
3. **Formula Derivation**: Understand how formulas are derived, don't just memorize
4. **Numerical Practice**: Solve at least 5-10 numerical problems daily
5. **Real-world Connection**: Relate physics concepts to everyday phenomena

For specific topics:
- **Mechanics**: Focus on free body diagrams and Newton's laws
- **Electricity**: Master Ohm's law and circuit analysis
- **Optics**: Practice ray diagrams extensively

Which specific physics topic are you finding most challenging?"""
        else:
            return """Physics requires a systematic approach:

1. **Theory First**: Read the concept thoroughly before attempting problems
2. **Formula Understanding**: Know when and how to apply each formula
3. **Problem-Solving Steps**: Identify given data, required answer, applicable concepts
4. **Units and Dimensions**: Always check units in your final answer
5. **Graphical Analysis**: Many physics problems involve interpreting graphs

Key topics to master: Mechanics, Thermodynamics, Electricity & Magnetism, Optics, and Modern Physics. Regular practice with numerical problems is essential. What specific physics concept would you like me to explain?"""

    elif 'chemistry' in question_lower or 'chemistry' in context_topics:
        return """Chemistry success requires balancing theory and numerical skills:

**Organic Chemistry:**
- Learn reaction mechanisms, not just reactions
- Practice naming compounds daily
- Understand electron movement in reactions

**Inorganic Chemistry:**
- Focus on periodic trends and properties
- Memorize important compounds and their uses
- Practice coordination chemistry problems

**Physical Chemistry:**
- Strong math foundation is essential
- Understand concepts before applying formulas
- Practice numerical problems regularly

**Study Tips:**
1. Make reaction charts and flowcharts
2. Practice structure drawing daily
3. Solve previous year questions topic-wise
4. Create formula sheets for physical chemistry

Which branch of chemistry would you like specific guidance on?"""

    elif 'biology' in question_lower or 'biology' in context_topics:
        return """Biology requires a different approach compared to Physics and Chemistry:

**Effective Biology Study Methods:**
1. **Diagram Practice**: Draw and label diagrams daily (especially for NEET)
2. **Flowcharts**: Create process flowcharts for complex cycles
3. **Mnemonics**: Use memory techniques for classifications and lists
4. **NCERT Focus**: 90% of biology questions come from NCERT
5. **Regular Revision**: Biology requires frequent revision due to vast content

**Key Areas to Focus:**
- **Botany**: Plant anatomy, physiology, reproduction
- **Zoology**: Human physiology, animal diversity
- **Genetics**: Inheritance patterns, molecular biology
- **Ecology**: Environmental biology and evolution

**Study Schedule**: Spend 2-3 hours daily on biology, with 1 hour for diagrams and 1-2 hours for theory. Which biology topic would you like help with?"""

    elif 'math' in question_lower or 'mathematics' in question_lower or 'math' in context_topics:
        return """Mathematics requires consistent practice and conceptual clarity:

**Effective Math Study Strategy:**
1. **Concept Clarity**: Understand the 'why' behind each formula
2. **Daily Practice**: Solve 15-20 problems daily across different topics
3. **Formula Derivation**: Derive important formulas yourself
4. **Previous Year Analysis**: Identify frequently asked question patterns
5. **Time Management**: Practice solving problems within time limits

**Key Topics for Competitive Exams:**
- **Algebra**: Quadratic equations, sequences, series
- **Calculus**: Limits, derivatives, integrals
- **Coordinate Geometry**: Straight lines, circles, conic sections
- **Trigonometry**: Identities, equations, inverse functions
- **Statistics & Probability**: Data analysis and probability distributions

**Practice Schedule**: 2-3 hours daily with focus on weak areas. Which math topic would you like specific guidance on?"""

    # Motivational and general guidance
    elif any(word in question_lower for word in ['motivation', 'stress', 'anxiety', 'confidence', 'fear', 'worried']):
        return """I understand exam preparation can be stressful. Here's some encouragement:

**Remember:**
- Every expert was once a beginner
- Consistent effort beats sporadic intensity
- Mistakes are learning opportunities, not failures
- Your pace of learning is unique to you

**Stress Management Tips:**
1. **Break Down Goals**: Set daily and weekly targets
2. **Celebrate Small Wins**: Acknowledge your progress
3. **Maintain Balance**: Include breaks, exercise, and hobbies
4. **Positive Self-Talk**: Replace "I can't" with "I'm learning"
5. **Seek Support**: Don't hesitate to ask for help

**Daily Routine:**
- Start with easier topics to build confidence
- End with revision of completed topics
- Take regular breaks to avoid burnout

You're capable of achieving your goals! What specific aspect of your preparation would you like to discuss?"""

    # Default helpful response
    else:
        return f"""I'd be happy to help you with your studies! Based on your question about "{question}", I can provide guidance on:

📚 **Study Techniques & Methods**
🔬 **Subject-specific Help** (Physics, Chemistry, Biology, Mathematics)
📝 **Exam Strategies** (NEET, JEE, Board Exams)
⏰ **Time Management & Planning**
💪 **Motivation & Stress Management**

Could you be more specific about what you'd like help with? For example:
- Which subject or topic are you struggling with?
- What type of study technique are you looking for?
- Are you preparing for a specific exam?

I'm here to support your learning journey! 🎯"""

def generate_general_mcq_ollama(topic, subject=None, num_questions=5):
    """Generate MCQ using Ollama without PDF content (general knowledge)"""
    if not OLLAMA_AVAILABLE:
        return "MCQ generation not available. Please install ollama package."

    subject_context = f" for {subject} subject" if subject else ""

    prompt = f"""Generate {num_questions} multiple-choice questions about {topic}{subject_context}.

Format the response as a valid JSON array where each question object has these exact fields:
[
    {{
        "question": "Question text here",
        "options": ["A. Option A text", "B. Option B text", "C. Option C text", "D. Option D text"],
        "correct_answer": "A",
        "explanation": "Detailed explanation of why this answer is correct",
        "difficulty": "medium"
    }},
    ...
]

Requirements:
- Each question must have exactly 4 options labeled A, B, C, D
- The correct_answer must be one of: A, B, C, or D
- Difficulty must be one of: easy, medium, hard
- Questions should be educational and appropriate for exam preparation
- Ensure the JSON is properly formatted and valid
- Do not include any text before or after the JSON array
"""

    try:
        response = ollama.chat(model='llama3.2:1b', messages=[{'role': 'user', 'content': prompt}])
        return response['message']['content']
    except Exception as e:
        print(f"Error generating general MCQ with Ollama: {e}")
        return f"Error generating MCQ: {str(e)}"

def generate_sample_questions_for_subject(subject_id, num_questions):
    """Generate sample MCQ questions when Ollama is not available"""
    from app.models.exam import Subject, QuestionWithOptions
    from app import db

    # Check if subject exists
    subject = Subject.query.get(subject_id)
    if not subject:
        return jsonify({
            'status': 'error',
            'message': 'Subject not found'
        }), 404

    # Get subject name and category for context
    subject_name = subject.subject_name
    category_name = subject.category.name if subject.category else 'General'

    # Check if questions already exist for this subject
    existing_questions = QuestionWithOptions.query.filter_by(subject_id=subject_id).count()

    if existing_questions >= num_questions:
        return jsonify({
            'status': 'success',
            'message': f'Subject already has {existing_questions} questions. Using existing questions.',
            'questions_generated': 0,
            'existing_questions': existing_questions,
            'total_questions': existing_questions
        })

    # Sample question templates based on subject and category
    sample_questions = get_sample_questions_by_subject(subject_name, category_name)

    # Calculate how many new questions to generate
    questions_to_generate = min(num_questions - existing_questions, len(sample_questions))

    # Store questions in database
    stored_questions = []
    for i in range(questions_to_generate):
        question_data = sample_questions[i % len(sample_questions)]

        try:
            # Create new question
            new_question = QuestionWithOptions(
                subject_id=subject_id,
                text=question_data['question'],
                difficulty=question_data.get('difficulty', 'medium'),
                option_a=question_data['options'][0],
                option_b=question_data['options'][1],
                option_c=question_data['options'][2],
                option_d=question_data['options'][3],
                correct_option=question_data['correct_answer'],
                explanation=question_data.get('explanation', '')
            )

            db.session.add(new_question)
            stored_questions.append(new_question)

        except Exception as e:
            print(f"Error storing sample question: {e}")
            continue

    # Commit all questions to database
    try:
        db.session.commit()
        questions_generated = len(stored_questions)
        total_questions = existing_questions + questions_generated

        return jsonify({
            'status': 'success',
            'message': f'Successfully generated and stored {questions_generated} sample questions for {subject_name}',
            'questions_generated': questions_generated,
            'existing_questions': existing_questions,
            'total_questions': total_questions,
            'subject_name': subject_name,
            'category_name': category_name,
            'note': 'Sample questions generated (Ollama not available)'
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({
            'status': 'error',
            'message': f'Failed to store sample questions in database: {str(e)}'
        }), 500


def get_sample_questions_by_subject(subject_name, category_name):
    """Get sample questions based on subject and category"""

    # Physics questions
    if 'physics' in subject_name.lower():
        return [
            {
                'question': 'What is the SI unit of force?',
                'options': ['Newton', 'Joule', 'Watt', 'Pascal'],
                'correct_answer': 'A',
                'explanation': 'The SI unit of force is Newton (N), named after Sir Isaac Newton.',
                'difficulty': 'easy'
            },
            {
                'question': 'Which law states that for every action, there is an equal and opposite reaction?',
                'options': ['First law of motion', 'Second law of motion', 'Third law of motion', 'Law of gravitation'],
                'correct_answer': 'C',
                'explanation': 'Newton\'s third law of motion states that for every action, there is an equal and opposite reaction.',
                'difficulty': 'medium'
            },
            {
                'question': 'What is the acceleration due to gravity on Earth?',
                'options': ['9.8 m/s²', '10 m/s²', '8.9 m/s²', '11 m/s²'],
                'correct_answer': 'A',
                'explanation': 'The acceleration due to gravity on Earth is approximately 9.8 m/s².',
                'difficulty': 'easy'
            }
        ]

    # Chemistry questions
    elif 'chemistry' in subject_name.lower():
        return [
            {
                'question': 'What is the chemical symbol for gold?',
                'options': ['Go', 'Gd', 'Au', 'Ag'],
                'correct_answer': 'C',
                'explanation': 'The chemical symbol for gold is Au, derived from the Latin word "aurum".',
                'difficulty': 'easy'
            },
            {
                'question': 'Which gas is most abundant in Earth\'s atmosphere?',
                'options': ['Oxygen', 'Carbon dioxide', 'Nitrogen', 'Argon'],
                'correct_answer': 'C',
                'explanation': 'Nitrogen makes up about 78% of Earth\'s atmosphere.',
                'difficulty': 'medium'
            },
            {
                'question': 'What is the pH of pure water at 25°C?',
                'options': ['6', '7', '8', '9'],
                'correct_answer': 'B',
                'explanation': 'Pure water has a pH of 7 at 25°C, which is considered neutral.',
                'difficulty': 'easy'
            }
        ]

    # Biology questions
    elif 'biology' in subject_name.lower():
        return [
            {
                'question': 'What is the powerhouse of the cell?',
                'options': ['Nucleus', 'Mitochondria', 'Ribosome', 'Endoplasmic reticulum'],
                'correct_answer': 'B',
                'explanation': 'Mitochondria are called the powerhouse of the cell because they produce ATP energy.',
                'difficulty': 'easy'
            },
            {
                'question': 'Which process do plants use to make their own food?',
                'options': ['Respiration', 'Photosynthesis', 'Transpiration', 'Digestion'],
                'correct_answer': 'B',
                'explanation': 'Photosynthesis is the process by which plants convert sunlight into chemical energy.',
                'difficulty': 'easy'
            },
            {
                'question': 'How many chambers does a human heart have?',
                'options': ['2', '3', '4', '5'],
                'correct_answer': 'C',
                'explanation': 'The human heart has four chambers: two atria and two ventricles.',
                'difficulty': 'medium'
            }
        ]

    # Mathematics questions
    elif 'mathematics' in subject_name.lower() or 'math' in subject_name.lower():
        return [
            {
                'question': 'What is the value of π (pi) approximately?',
                'options': ['3.14', '3.41', '2.14', '4.13'],
                'correct_answer': 'A',
                'explanation': 'The value of π (pi) is approximately 3.14159...',
                'difficulty': 'easy'
            },
            {
                'question': 'What is the derivative of x²?',
                'options': ['x', '2x', 'x²', '2x²'],
                'correct_answer': 'B',
                'explanation': 'The derivative of x² with respect to x is 2x.',
                'difficulty': 'medium'
            },
            {
                'question': 'What is the sum of angles in a triangle?',
                'options': ['90°', '180°', '270°', '360°'],
                'correct_answer': 'B',
                'explanation': 'The sum of all angles in any triangle is always 180°.',
                'difficulty': 'easy'
            }
        ]

    # Default general questions
    else:
        return [
            {
                'question': f'What is a fundamental concept in {subject_name}?',
                'options': ['Basic principles', 'Advanced theories', 'Complex formulas', 'Simple rules'],
                'correct_answer': 'A',
                'explanation': f'Basic principles form the foundation of {subject_name}.',
                'difficulty': 'medium'
            },
            {
                'question': f'Which approach is most effective for studying {subject_name}?',
                'options': ['Memorization only', 'Understanding concepts', 'Skipping basics', 'Random reading'],
                'correct_answer': 'B',
                'explanation': f'Understanding concepts is crucial for mastering {subject_name}.',
                'difficulty': 'medium'
            },
            {
                'question': f'What is the importance of practice in {subject_name}?',
                'options': ['Not important', 'Somewhat important', 'Very important', 'Depends on mood'],
                'correct_answer': 'C',
                'explanation': f'Regular practice is very important for excelling in {subject_name}.',
                'difficulty': 'easy'
            }
        ]


# API Endpoints
@ai_bp.route('/generate-mcq-for-exam', methods=['POST'])
def generate_mcq_for_exam():
    """Generate MCQ questions for a specific subject and store them in database"""
    from app.models.exam import Subject, QuestionWithOptions
    from app import db

    data = request.get_json() or {}

    if not OLLAMA_AVAILABLE:
        # Fallback: Create sample questions when Ollama is not available
        return generate_sample_questions_for_subject(data.get('subject_id'), data.get('num_questions', 50))

    # Get parameters from request
    subject_id = data.get('subject_id')
    num_questions = data.get('num_questions', 50)
    use_pdf_content = data.get('use_pdf_content', True)

    # Validate required parameters
    if not subject_id:
        return jsonify({
            'status': 'error',
            'message': 'subject_id is required'
        }), 400

    # Validate num_questions
    if not isinstance(num_questions, int) or num_questions < 1 or num_questions > 200:
        return jsonify({
            'status': 'error',
            'message': 'num_questions must be an integer between 1 and 200'
        }), 400

    # Check if subject exists
    subject = Subject.query.get(subject_id)
    if not subject:
        return jsonify({
            'status': 'error',
            'message': 'Subject not found'
        }), 404

    # Get subject name and category for context
    subject_name = subject.subject_name
    category_name = subject.exam_category.name if subject.exam_category else 'General'

    # Create topic from subject and category
    topic = f"{category_name} {subject_name}"

    try:
        # Check if questions already exist for this subject
        existing_questions = QuestionWithOptions.query.filter_by(subject_id=subject_id).count()

        if existing_questions >= num_questions:
            return jsonify({
                'status': 'success',
                'message': f'Subject already has {existing_questions} questions. Using existing questions.',
                'questions_generated': 0,
                'existing_questions': existing_questions,
                'total_questions': existing_questions
            })

        # Calculate how many new questions to generate
        questions_to_generate = min(num_questions - existing_questions, 20)  # Generate max 20 at a time

        # Generate MCQ questions
        if use_pdf_content:
            # Try to use PDF content if available
            global texts, sources, embeddings
            embeddings, texts, sources = load_or_create_index()

            if texts:
                # Generate MCQs from PDF content
                mcq_content, used_sources = generate_mcq_from_combined_content(
                    texts, sources, num_questions=questions_to_generate, topic=topic, subject=subject_name
                )
            else:
                # Fallback to general knowledge
                mcq_content = generate_general_mcq_ollama(topic, subject_name, questions_to_generate)
        else:
            # Generate from general knowledge
            mcq_content = generate_general_mcq_ollama(topic, subject_name, questions_to_generate)

        # Parse the generated MCQ content
        try:
            import json
            if isinstance(mcq_content, str):
                # Clean the response to extract JSON
                mcq_content = mcq_content.strip()
                if mcq_content.startswith('```json'):
                    mcq_content = mcq_content[7:]
                if mcq_content.endswith('```'):
                    mcq_content = mcq_content[:-3]
                mcq_content = mcq_content.strip()

                questions_data = json.loads(mcq_content)
            else:
                questions_data = mcq_content

            if not isinstance(questions_data, list):
                raise ValueError("Generated content is not a list of questions")

        except (json.JSONDecodeError, ValueError) as e:
            return jsonify({
                'status': 'error',
                'message': f'Failed to parse generated MCQ content: {str(e)}',
                'raw_content': mcq_content[:500] if isinstance(mcq_content, str) else str(mcq_content)[:500]
            }), 500

        # Store questions in database
        stored_questions = []
        for question_data in questions_data:
            try:
                # Validate question structure
                required_fields = ['question', 'options', 'correct_answer', 'explanation']
                for field in required_fields:
                    if field not in question_data:
                        print(f"Warning: Missing field '{field}' in question data")
                        continue

                # Extract options
                options = question_data.get('options', [])
                if len(options) != 4:
                    print(f"Warning: Question has {len(options)} options instead of 4")
                    continue

                # Create new question
                new_question = QuestionWithOptions(
                    subject_id=subject_id,
                    text=question_data['question'],
                    difficulty=question_data.get('difficulty', 'medium'),
                    option_a=options[0].replace('A. ', '').strip() if len(options) > 0 else '',
                    option_b=options[1].replace('B. ', '').strip() if len(options) > 1 else '',
                    option_c=options[2].replace('C. ', '').strip() if len(options) > 2 else '',
                    option_d=options[3].replace('D. ', '').strip() if len(options) > 3 else '',
                    correct_option=question_data['correct_answer'].upper(),
                    explanation=question_data.get('explanation', '')
                )

                db.session.add(new_question)
                stored_questions.append(new_question)

            except Exception as e:
                print(f"Error storing question: {e}")
                continue

        # Commit all questions to database
        try:
            db.session.commit()
            questions_generated = len(stored_questions)
            total_questions = existing_questions + questions_generated

            return jsonify({
                'status': 'success',
                'message': f'Successfully generated and stored {questions_generated} questions for {subject_name}',
                'questions_generated': questions_generated,
                'existing_questions': existing_questions,
                'total_questions': total_questions,
                'subject_name': subject_name,
                'category_name': category_name
            })

        except Exception as e:
            db.session.rollback()
            return jsonify({
                'status': 'error',
                'message': f'Failed to store questions in database: {str(e)}'
            }), 500

    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': f'Error generating MCQ questions: {str(e)}'
        }), 500


@ai_bp.route('/generate-mcq-ollama', methods=['POST'])
def generate_mcq_ollama():
    """Generate MCQ questions using Ollama with optional PDF content"""
    global texts, sources, embeddings

    if not OLLAMA_AVAILABLE:
        return jsonify({
            'status': 'error',
            'message': 'Ollama is not available. Please install the ollama package.'
        }), 500

    data = request.get_json() or {}

    # Get parameters from request
    topic = data.get('topic', 'general knowledge')
    subject = data.get('subject', None)
    num_questions = data.get('num_questions', 5)
    use_pdf_content = data.get('use_pdf_content', True)

    # Validate num_questions
    if not isinstance(num_questions, int) or num_questions < 1 or num_questions > 20:
        return jsonify({
            'status': 'error',
            'message': 'num_questions must be an integer between 1 and 20'
        }), 400

    try:
        if use_pdf_content:
            # Try to use PDF content if available
            embeddings, texts, sources = load_or_create_index()

            if texts:
                # Generate MCQs from PDF content
                mcq_content, used_sources = generate_mcq_from_combined_content(
                    texts, sources, num_questions=num_questions, topic=topic, subject=subject
                )

                # Try to parse as JSON to validate
                try:
                    import json
                    mcq_json = json.loads(mcq_content)
                    result = {
                        'status': 'success',
                        'questions': mcq_json,
                        'sources_used': used_sources,
                        'total_pdfs_processed': len(texts),
                        'generation_method': 'pdf_content'
                    }
                except json.JSONDecodeError:
                    # If JSON parsing fails, return raw content with warning
                    result = {
                        'status': 'partial_success',
                        'raw_content': mcq_content,
                        'sources_used': used_sources,
                        'total_pdfs_processed': len(texts),
                        'generation_method': 'pdf_content',
                        'warning': 'Generated content may not be in valid JSON format'
                    }
            else:
                # No PDF content available, use general knowledge
                mcq_content = generate_general_mcq_ollama(topic, subject, num_questions)

                try:
                    import json
                    mcq_json = json.loads(mcq_content)
                    result = {
                        'status': 'success',
                        'questions': mcq_json,
                        'generation_method': 'general_knowledge',
                        'message': 'No PDF content available, generated from general knowledge'
                    }
                except json.JSONDecodeError:
                    result = {
                        'status': 'partial_success',
                        'raw_content': mcq_content,
                        'generation_method': 'general_knowledge',
                        'warning': 'Generated content may not be in valid JSON format'
                    }
        else:
            # Generate from general knowledge only
            mcq_content = generate_general_mcq_ollama(topic, subject, num_questions)

            try:
                import json
                mcq_json = json.loads(mcq_content)
                result = {
                    'status': 'success',
                    'questions': mcq_json,
                    'generation_method': 'general_knowledge'
                }
            except json.JSONDecodeError:
                result = {
                    'status': 'partial_success',
                    'raw_content': mcq_content,
                    'generation_method': 'general_knowledge',
                    'warning': 'Generated content may not be in valid JSON format'
                }

    except Exception as e:
        result = {
            'status': 'error',
            'message': f'Error generating MCQs: {str(e)}'
        }

    return jsonify(result)

@ai_bp.route('/generate-mcq-from-pdfs', methods=['GET', 'POST'])
def generate_mcq_from_pdfs():
    """Generate MCQ questions from PDF files in the pdfs directory using Ollama"""
    global texts, sources, embeddings

    if not OLLAMA_AVAILABLE:
        return jsonify({
            'status': 'error',
            'message': 'Ollama is not available. Please install the ollama package.'
        }), 500

    # Get parameters from request (for POST) or use defaults (for GET)
    if request.method == 'POST':
        data = request.get_json() or {}
        num_questions = data.get('num_questions', 5)
        topic = data.get('topic', None)
        subject = data.get('subject', None)
    else:
        num_questions = int(request.args.get('num_questions', 5))
        topic = request.args.get('topic', None)
        subject = request.args.get('subject', None)

    # Validate num_questions
    if not isinstance(num_questions, int) or num_questions < 1 or num_questions > 20:
        return jsonify({
            'status': 'error',
            'message': 'num_questions must be an integer between 1 and 20'
        }), 400

    embeddings, texts, sources = load_or_create_index()

    if not texts:
        return jsonify({
            'status': 'error',
            'message': f'No PDF files found in the pdfs directory: {PDF_FOLDER}'
        })

    try:
        # Generate MCQs from combined content of all PDFs
        mcq_content, used_sources = generate_mcq_from_combined_content(
            texts, sources, num_questions=num_questions, topic=topic, subject=subject
        )

        # Try to parse as JSON
        try:
            mcq_json = json.loads(mcq_content)
            result = {
                'status': 'success',
                'questions': mcq_json,
                'sources_used': used_sources,
                'total_pdfs_processed': len(texts),
                'total_sources_used_for_mcq': len(used_sources),
                'pdf_folder': PDF_FOLDER
            }
        except json.JSONDecodeError:
            # If JSON parsing fails, return raw content with warning
            result = {
                'status': 'partial_success',
                'raw_content': mcq_content,
                'sources_used': used_sources,
                'total_pdfs_processed': len(texts),
                'total_sources_used_for_mcq': len(used_sources),
                'pdf_folder': PDF_FOLDER,
                'warning': 'Generated content may not be in valid JSON format'
            }

    except Exception as e:
        result = {
            'status': 'error',
            'message': f'Error generating MCQs: {str(e)}',
            'total_pdfs_processed': len(texts),
            'pdf_folder': PDF_FOLDER
        }

    return jsonify(result)

@ai_bp.route('/status', methods=['GET'])
def ai_status():
    """Check the status of AI services and dependencies"""
    status = {
        'status': 'running',
        'dependencies': {
            'ollama': OLLAMA_AVAILABLE,
            'PyPDF2': PYPDF2_AVAILABLE,
            'sentence_transformers': SENTENCE_TRANSFORMERS_AVAILABLE
        },
        'pdf_folder': PDF_FOLDER,
        'pdf_folder_exists': os.path.exists(PDF_FOLDER)
    }

    # Check if PDF folder has any PDF files
    if os.path.exists(PDF_FOLDER):
        pdf_files = [f for f in os.listdir(PDF_FOLDER) if f.endswith('.pdf')]
        status['pdf_files_count'] = len(pdf_files)
        status['pdf_files'] = pdf_files[:10]  # Show first 10 files
    else:
        status['pdf_files_count'] = 0
        status['pdf_files'] = []

    return jsonify(status)
