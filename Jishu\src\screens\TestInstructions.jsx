import React, { useState } from 'react';
import {
  StyleSheet,
  Text,
  View,
  ScrollView,
  TouchableOpacity,
  Alert,
  SafeAreaView,
  ActivityIndicator,
  Switch
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { userExamsAPI, mcqGenerationAPI } from '../services/api';

// Define consistent colors
const COLORS = {
  primary: '#3461eb',
  secondary: '#6200ee',
  background: '#F9FAFA',
  card: '#FFFFFF',
  text: '#1C1D22',
  textSecondary: '#3C3F4A',
  border: '#EEEFF2',
  success: '#4CAF50',
  warning: '#FFC107',
  error: '#F44336'
};

const TestInstructions = ({ route }) => {
  const {
    subjectId,
    subjectName,
    categoryId,
    categoryName,
    isFullMock,
    questionCount: passedQuestionCount,
    durationMinutes: passedDurationMinutes,
    price,
    currency,
    formattedPrice
  } = route.params;
  const navigation = useNavigation();
  const [loading, setLoading] = useState(false);
  const [agreed, setAgreed] = useState(false);

  // Determine question count based on exam type and category
  const getQuestionCount = () => {
    // Use passed question count if available
    if (passedQuestionCount) return passedQuestionCount;

    if (isFullMock) {
      if (categoryName === 'NEET') return 180;
      if (categoryName === 'JEE') return 90;
      if (categoryName === 'CET') return 120;
      if (categoryName === 'GATE') return 65;
      return 100;
    }
    return 50; // Default for subject tests
  };

  // Determine time limit based on exam type and category
  const getTimeLimit = () => {
    // Use passed duration if available
    if (passedDurationMinutes) {
      if (passedDurationMinutes >= 60) {
        const hours = Math.floor(passedDurationMinutes / 60);
        const minutes = passedDurationMinutes % 60;
        return hours + (minutes > 0 ? ` hour${hours > 1 ? 's' : ''} ${minutes} minutes` : ` hour${hours > 1 ? 's' : ''}`);
      }
      return `${passedDurationMinutes} minutes`;
    }

    if (isFullMock) {
      if (categoryName === 'NEET') return '3 hours';
      if (categoryName === 'JEE') return '3 hours';
      if (categoryName === 'CET') return '2 hours';
      if (categoryName === 'GATE') return '3 hours';
      return '2 hours';
    }
    return '1 hour'; // Default for subject tests
  };

  const questionCount = getQuestionCount();
  const duration = getTimeLimit();

  // Create exam details object
  const examDetails = {
    name: `${subjectName} ${isFullMock ? 'Full Mock Test' : 'Practice Test'}`,
    category: categoryName,
    categoryName: categoryName,
    description: `${categoryName} ${subjectName} ${isFullMock ? 'Full Mock Test' : 'Practice Test'}`,
    duration: duration,
    questions: questionCount,
    price: formattedPrice || (price && price > 0 ? `${currency || '₹'} ${price}` : 'Free'),
    icon: isFullMock ? 'stethoscope' : 'book-open-variant'
  };

  // If required params are missing, show an error
  if (!subjectId || !subjectName || !categoryName) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.errorContainer}>
          <MaterialIcons name="error-outline" size={48} color={COLORS.error} />
          <Text style={styles.errorText}>Error: Missing required exam parameters</Text>
          <TouchableOpacity style={styles.backButtonLarge} onPress={() => navigation.goBack()}>
            <Text style={styles.backButtonText}>Go Back</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  // Define instructions based on exam type
  const generalInstructions = [
    `This test contains ${examDetails.questions} multiple-choice questions.`,
    `You have ${examDetails.duration} to complete the test.`,
    "Each correct answer gives +4 marks.",
    "Each incorrect answer deducts -1 mark.",
    "You can mark questions for review and return to them later.",
    "Once you submit the test, you cannot return to it.",
    "Your results will be available immediately after submission.",
    "There is no limit on the number of attempts for purchased tests."
  ];

  // Category-specific instructions
  const categoryInstructions = {
    'NEET': [
      "Focus on Biology (50%), Chemistry (25%), and Physics (25%).",
      "Pay special attention to NCERT-based questions.",
      "Time management is crucial due to the high number of questions.",
      "Marking scheme: +4 for correct answers, -1 for incorrect answers.",
      "No marks are deducted for unattempted questions."
    ],
    'JEE_Advance': [
      "Questions may have multiple correct options.",
      "Some questions may have numerical answers.",
      "Advanced problem-solving skills are tested.",
      "Marking scheme: +4 for correct answers, -1 for incorrect answers.",
      "No marks are deducted for unattempted questions."
    ],
    'JEE_Mains': [
      "Focus equally on Physics, Chemistry, and Mathematics.",
      "Questions are designed to test conceptual understanding.",
      "Speed and accuracy are both important.",
      "Marking scheme: +4 for correct answers, -1 for incorrect answers.",
      "No marks are deducted for unattempted questions."
    ],
    'JEE': [
      "Focus equally on Physics, Chemistry, and Mathematics.",
      "Questions are designed to test conceptual understanding.",
      "Speed and accuracy are both important.",
      "Marking scheme: +4 for correct answers, -1 for incorrect answers.",
      "No marks are deducted for unattempted questions."
    ],
    'GATE': [
      "Engineering mathematics and core subject knowledge is tested.",
      "Questions include multiple choice and numerical answer type.",
      "Virtual calculator will be provided for calculations.",
      "Marking scheme: +1 or +2 for correct answers, -1/3 or -2/3 for incorrect answers.",
      "No marks are deducted for unattempted questions."
    ],
    'CET': [
      "State-specific syllabus is covered.",
      "Questions are generally of moderate difficulty.",
      "Focus on speed as the time per question is limited.",
      "Marking scheme: +4 for correct answers, -1 for incorrect answers.",
      "No marks are deducted for unattempted questions."
    ]
  };

  // Get category-specific instructions
  const specificInstructions = examDetails.category ? categoryInstructions[examDetails.category] || [] : [];

  // Handle agreement toggle
  const handleAgreementToggle = (value) => {
    setAgreed(value);
  };

  // Start the exam
  const handleProceedToPayment = () => {
    if (!agreed) {
      Alert.alert("Agreement Required", "Please agree to the terms and conditions to proceed.");
      return;
    }

    // Show confirmation alert
    Alert.alert(
      "Start Exam",
      `You are about to start the ${examDetails.name}. Are you ready to begin?`,
      [
        { text: "Cancel", style: "cancel" },
        { text: "Start Now", onPress: handlePayment }
      ]
    );
  };

  // Handle starting the exam
  const handlePayment = async () => {
    try {
      setLoading(true);

      console.log('Starting exam with subject ID:', subjectId, 'Type:', typeof subjectId);

      // Validate subject ID
      if (!subjectId) {
        throw new Error('Subject ID is missing or invalid');
      }

      // Step 1: Generate MCQ questions for this subject
      console.log('Generating MCQ questions for the exam...');
      try {
        const mcqResponse = await mcqGenerationAPI.generateMCQForExam(
          subjectId,
          questionCount,
          true // Use PDF content if available
        );

        console.log('MCQ generation response:', mcqResponse);

        if (mcqResponse.status === 'error') {
          console.warn('MCQ generation failed:', mcqResponse.message);
          // Continue with exam even if MCQ generation fails
        } else {
          console.log(`Successfully prepared ${mcqResponse.total_questions || questionCount} questions for the exam`);
        }
      } catch (mcqError) {
        console.error('Error generating MCQ questions:', mcqError);
        // Continue with exam even if MCQ generation fails
        console.log('Continuing with exam despite MCQ generation error');
      }

      // Step 2: Try to purchase the exam through API
      let userExamId;
      try {
        const purchaseResponse = await userExamsAPI.purchaseExam(subjectId);
        console.log('Purchase response:', purchaseResponse);
        userExamId = purchaseResponse.exam.id;
      } catch (purchaseError) {
        console.error('Error purchasing exam:', purchaseError);
        // If purchase fails, check if already purchased
        try {
          const userExams = await userExamsAPI.getAll();
          console.log('User exams:', userExams);
          const existingExam = userExams.find(exam => exam.subject_id === subjectId);

          if (existingExam) {
            userExamId = existingExam.id;
            console.log('Found existing exam purchase:', existingExam);
          } else {
            // For development, create a temporary ID
            userExamId = Date.now();
            console.log('Using temporary user exam ID for development:', userExamId);
          }
        } catch (error) {
          console.error('Error checking existing purchases:', error);
          // For development, create a temporary ID
          userExamId = Date.now();
          console.log('Using temporary user exam ID for development:', userExamId);
        }
      }

      setLoading(false);

      // Step 3: Navigate to the exam questions screen
      navigation.navigate('ExamQuestionsScreen', {
        subjectId,
        subjectName,
        categoryId,
        category: categoryName,
        isFullMock,
        userExamId,
        questionCount
      });

    } catch (error) {
      setLoading(false);
      Alert.alert("Error", "There was an error starting the exam. Please try again.");
      console.error(error);
    }
  };

  // Handle back navigation
  const handleBack = () => {
    navigation.goBack();
  };

  return (
    <SafeAreaView style={styles.container}>

      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={COLORS.primary} />
          <Text style={styles.loadingText}>Generating questions with AI...</Text>
          <Text style={styles.loadingSubText}>This may take a few moments</Text>
        </View>
      ) : (
        <>
          <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
            {/* Exam details card */}
            <View style={styles.examCard}>
              <View style={styles.examHeader}>
                <View style={styles.examIconContainer}>
                  {['atom', 'flask-empty-outline', 'virus-outline', 'dna', 'calculator-variant-outline', 'stethoscope', 'laptop', 'chip', 'book-open-variant'].includes(examDetails.icon) ? (
                    <MaterialCommunityIcons name={examDetails.icon} size={30} color={COLORS.primary} />
                  ) : (
                    <MaterialIcons name={examDetails.icon || 'school'} size={30} color={COLORS.primary} />
                  )}
                </View>
                <View style={styles.examTitleContainer}>
                  <Text style={styles.examTitle}>{examDetails.name}</Text>
                  <Text style={styles.examCategory}>{examDetails.categoryName}</Text>
                </View>
                <View style={styles.priceBadge}>
                  <Text style={styles.priceText}>{examDetails.price}</Text>
                </View>
              </View>

              <View style={styles.examDetails}>
                <View style={styles.detailItem}>
                  <MaterialIcons name="help-outline" size={18} color={COLORS.textSecondary} />
                  <Text style={styles.detailText}>{examDetails.questions} questions</Text>
                </View>
                <View style={styles.detailItem}>
                  <MaterialIcons name="access-time" size={18} color={COLORS.textSecondary} />
                  <Text style={styles.detailText}>{examDetails.duration}</Text>
                </View>
              </View>
            </View>

            {/* Instructions sections */}
            <View style={styles.instructionsSection}>
              <Text style={styles.sectionTitle}>General Instructions</Text>
              {generalInstructions.map((item, index) => (
                <View key={`general-${index}`} style={styles.instructionItem}>
                  <MaterialIcons name="check-circle" size={18} color={COLORS.primary} />
                  <Text style={styles.instructionText}>{item}</Text>
                </View>
              ))}
            </View>

            <View style={styles.instructionsSection}>
              <Text style={styles.sectionTitle}>{examDetails.categoryName || 'Exam'} Specific Instructions</Text>
              {specificInstructions.map((item, index) => (
                <View key={`specific-${index}`} style={styles.instructionItem}>
                  <MaterialIcons name="info" size={18} color={COLORS.secondary} />
                  <Text style={styles.instructionText}>{item}</Text>
                </View>
              ))}
            </View>

            {/* Agreement section */}
            <View style={styles.agreementContainer}>
              <Switch
                value={agreed}
                onValueChange={handleAgreementToggle}
                trackColor={{ false: COLORS.border, true: COLORS.primary }}
                thumbColor={agreed ? '#fff' : '#f4f3f4'}
              />
              <Text style={styles.agreementText}>
                I have read and agree to the test instructions and terms of use.
              </Text>
            </View>
          </ScrollView>

          <View style={styles.footer}>
            <TouchableOpacity
              style={[styles.proceedButton, !agreed && styles.proceedButtonDisabled]}
              onPress={handleProceedToPayment}
              disabled={!agreed}
            >
              <Text style={styles.buttonText}>Start Exam</Text>
            </TouchableOpacity>
          </View>
        </>
      )}
    </SafeAreaView>
  );
};

export default TestInstructions;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.border,
    backgroundColor: COLORS.card,
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: COLORS.text,
  },
  placeholder: {
    width: 40,
  },
  scrollView: {
    flex: 1,
    padding: 16,
  },
  examCard: {
    backgroundColor: COLORS.card,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  examHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  examIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 8,
    backgroundColor: COLORS.border,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  examTitleContainer: {
    flex: 1,
  },
  examTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: COLORS.text,
    marginBottom: 4,
  },
  examCategory: {
    fontSize: 14,
    color: COLORS.textSecondary,
  },
  priceBadge: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 12,
    backgroundColor: COLORS.border,
  },
  priceText: {
    fontSize: 14,
    fontWeight: 'bold',
    color: COLORS.text,
  },
  examDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: COLORS.border,
  },
  detailItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  detailText: {
    fontSize: 14,
    color: COLORS.textSecondary,
    marginLeft: 6,
  },
  instructionsSection: {
    backgroundColor: COLORS.card,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: COLORS.text,
    marginBottom: 12,
  },
  instructionItem: {
    flexDirection: 'row',
    marginBottom: 12,
    alignItems: 'flex-start',
  },
  instructionText: {
    fontSize: 14,
    color: COLORS.text,
    marginLeft: 8,
    flex: 1,
    lineHeight: 20,
  },
  agreementContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.card,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
  },
  agreementText: {
    flex: 1,
    marginLeft: 12,
    fontSize: 14,
    color: COLORS.text,
  },
  footer: {
    padding: 16,
    backgroundColor: COLORS.card,
    borderTopWidth: 1,
    borderTopColor: COLORS.border,
  },
  proceedButton: {
    backgroundColor: COLORS.primary,
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
  },
  proceedButtonDisabled: {
    backgroundColor: COLORS.border,
  },
  buttonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: COLORS.textSecondary,
  },
  loadingSubText: {
    marginTop: 8,
    fontSize: 14,
    color: COLORS.textSecondary,
    textAlign: 'center',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  errorText: {
    marginTop: 12,
    fontSize: 16,
    color: COLORS.textSecondary,
  },
  backButtonLarge: {
    marginTop: 24,
    backgroundColor: COLORS.primary,
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 12,
  },
  backButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
});
