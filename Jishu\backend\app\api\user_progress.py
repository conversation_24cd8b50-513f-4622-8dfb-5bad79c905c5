from flask import request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from app import db
from app.api import bp
from app.models.exam import Question
from app.models.payment import Purchase
from app.models.user_progress import ExamAttempt
from datetime import datetime, timezone

@bp.route('/user/exams', methods=['GET'])
def get_user_exams():
    """Get all exams purchased by the current user"""
    try:
        # For development, make this endpoint accessible without JWT
        # In a real app, you would use @jwt_required() and get_jwt_identity()

        # Try to get the JWT identity if available
        try:
            from flask_jwt_extended import get_jwt_identity
            current_user_id = get_jwt_identity()
            print(f"JWT identity found: {current_user_id}")
        except Exception as jwt_error:
            print(f"JWT error: {str(jwt_error)}")
            # For development, use a default user ID
            current_user_id = 1
            print(f"Using default user ID: {current_user_id}")

        if not current_user_id:
            return jsonify({'error': 'Invalid user identity'}), 401

        # For development, if no purchases exist, return an empty array
        purchases = Purchase.query.filter_by(user_id=current_user_id).all()

        result = []
        for purchase in purchases:
            try:
                # Skip purchases with missing subjects
                if not purchase.subject:
                    continue

                result.append({
                    'id': purchase.id,
                    'user_id': purchase.user_id,
                    'subject_id': purchase.subject_id,
                    'subject': {
                        'id': purchase.subject.id,
                        'name': purchase.subject.subject_name,
                        'description': purchase.subject.description,
                        'category_id': purchase.subject.exam_category_id,
                        'category_name': purchase.subject.category.name if purchase.subject.category else None
                    },
                    'purchased_at': purchase.purchase_date.isoformat() if purchase.purchase_date else None,
                    'max_retakes': purchase.max_retakes,
                    'retakes_used': purchase.retake_used,
                    'retakes_remaining': purchase.max_retakes - purchase.retake_used,
                    'marks': purchase.marks,
                    'negative_marks': purchase.negative_marks,
                    'attempts': [attempt.to_dict() for attempt in ExamAttempt.query.filter_by(purchase_id=purchase.id).all()],
                    'attempt_count': ExamAttempt.query.filter_by(purchase_id=purchase.id).count()
                })
            except Exception as item_error:
                print(f"Error processing purchase {purchase.id}: {str(item_error)}")
                # Skip this purchase and continue with others
                continue

        return jsonify(result)
    except Exception as e:
        print(f"Error in get_user_exams: {str(e)}")
        return jsonify({'error': 'An error occurred while fetching user exams', 'details': str(e)}), 500

@bp.route('/user/exams/<int:purchase_id>/start', methods=['POST'])
@jwt_required()
def start_exam_attempt(purchase_id):
    """Start a new exam attempt (limited to max_retakes per purchase)"""
    current_user_id = get_jwt_identity()

    # Check if purchase exists and belongs to the current user
    purchase = Purchase.query.get_or_404(purchase_id)

    if purchase.user_id != current_user_id:
        return jsonify({'error': 'Unauthorized access to this exam'}), 403

    # Check if user has retakes remaining
    if purchase.retake_used >= purchase.max_retakes:
        return jsonify({
            'error': 'Maximum retakes limit reached for this exam',
            'retakes_used': purchase.retake_used,
            'max_retakes': purchase.max_retakes
        }), 400

    # Get questions for this subject
    questions = Question.query.filter_by(subject_id=purchase.subject_id).all()
    total_questions = len(questions)

    if total_questions == 0:
        return jsonify({'error': 'No questions available for this exam'}), 400

    # Calculate attempt number (previous attempts count + 1)
    attempt_number = ExamAttempt.query.filter_by(purchase_id=purchase_id).count() + 1

    # Increment retakes used
    purchase.retake_used += 1

    # Create new exam attempt
    attempt = ExamAttempt(
        user_id=current_user_id,
        purchase_id=purchase_id,
        attempt_number=attempt_number,
        total_questions=total_questions,
        started_at=datetime.now(timezone.utc)
    )

    db.session.add(attempt)
    db.session.commit()

    # Return attempt details and questions
    return jsonify({
        'attempt': attempt.to_dict(),
        'questions': [q.to_dict() for q in questions]
    })

@bp.route('/user/attempts/<int:attempt_id>/submit', methods=['POST'])
@jwt_required()
def submit_exam_attempt(attempt_id):
    """Submit an exam attempt with answers and calculate comprehensive scoring"""
    from app.models.exam import QuestionWithOptions
    from app.models.user_progress import UserAnswer

    current_user_id = get_jwt_identity()

    # Check if attempt exists and belongs to the current user
    attempt = ExamAttempt.query.get_or_404(attempt_id)

    if attempt.user_id != current_user_id:
        return jsonify({'error': 'Unauthorized access to this attempt'}), 403

    if attempt.completed_at:
        return jsonify({'error': 'This attempt has already been submitted'}), 400

    data = request.get_json() or {}

    if 'answers' not in data:
        return jsonify({'error': 'Answers are required'}), 400

    answers = data['answers']  # Format: [{'question_id': int, 'selected_option': 'A', 'time_spent_seconds': int}]
    time_taken = data.get('time_taken_seconds', 0)

    # Get the purchase to determine marks and negative marks
    purchase = Purchase.query.get(attempt.purchase_id)
    marks_per_question = purchase.marks if purchase else 4
    negative_marks = purchase.negative_marks if purchase else 1

    # Calculate comprehensive scoring
    correct_answers = 0
    wrong_answers = 0
    total_time_spent = 0
    answer_details = []

    # Process each answer
    for answer_data in answers:
        question_id = answer_data.get('question_id')
        selected_option = answer_data.get('selected_option')
        time_spent = answer_data.get('time_spent_seconds', 0)

        # Get the question from QuestionWithOptions table
        question = QuestionWithOptions.query.get(int(question_id))
        if not question:
            continue

        # Check if the selected option is correct
        is_correct = selected_option == question.correct_option

        if is_correct:
            correct_answers += 1
        else:
            wrong_answers += 1

        total_time_spent += time_spent

        # Store detailed user answer
        user_answer = UserAnswer(
            attempt_id=attempt_id,
            question_id=question_id,
            selected_option=selected_option,
            time_spent_seconds=time_spent
        )
        db.session.add(user_answer)

        # Collect answer details for response
        answer_details.append({
            'question_id': question_id,
            'question_text': question.text,
            'selected_option': selected_option,
            'correct_option': question.correct_option,
            'is_correct': is_correct,
            'explanation': question.explanation,
            'time_spent_seconds': time_spent,
            'options': {
                'A': question.option_a,
                'B': question.option_b,
                'C': question.option_c,
                'D': question.option_d
            }
        })

    # Calculate final score using the marks from the purchase
    score = (correct_answers * marks_per_question) - (wrong_answers * negative_marks)
    unattempted = attempt.total_questions - (correct_answers + wrong_answers)

    # Calculate percentage and additional metrics
    percentage = (correct_answers / attempt.total_questions * 100) if attempt.total_questions > 0 else 0
    avg_time_per_question = total_time_spent / len(answers) if len(answers) > 0 else 0

    # Update attempt with comprehensive data
    attempt.score = score
    attempt.correct_answers = correct_answers
    attempt.wrong_answers = wrong_answers
    attempt.unattempted = unattempted
    attempt.time_taken_seconds = time_taken
    attempt.completed_at = datetime.now(timezone.utc)

    db.session.commit()

    # Return comprehensive results
    result = attempt.to_dict()
    result.update({
        'percentage': round(percentage, 2),
        'avg_time_per_question': round(avg_time_per_question, 2),
        'total_time_spent_on_questions': total_time_spent,
        'marks_per_question': marks_per_question,
        'negative_marks': negative_marks,
        'answer_details': answer_details,
        'performance_metrics': {
            'accuracy': round(percentage, 2),
            'speed_score': calculate_speed_score(avg_time_per_question),
            'efficiency_score': calculate_efficiency_score(correct_answers, total_time_spent)
        }
    })

    return jsonify(result)

def calculate_speed_score(avg_time_per_question):
    """Calculate speed score based on average time per question"""
    # Ideal time is around 90 seconds per question
    ideal_time = 90
    if avg_time_per_question <= ideal_time:
        return min(100, (ideal_time / avg_time_per_question) * 100)
    else:
        return max(50, 100 - ((avg_time_per_question - ideal_time) / ideal_time) * 50)

def calculate_efficiency_score(correct_answers, total_time_spent):
    """Calculate efficiency score based on correct answers per minute"""
    if total_time_spent == 0:
        return 0

    time_in_minutes = total_time_spent / 60
    efficiency = correct_answers / time_in_minutes

    # Scale efficiency to 0-100 (assuming 1 correct answer per minute is good)
    return min(100, efficiency * 100)

@bp.route('/user/attempts', methods=['GET'])
@jwt_required()
def get_user_attempts():
    """Get all exam attempts by the current user"""
    current_user_id = get_jwt_identity()

    attempts = ExamAttempt.query.filter_by(user_id=current_user_id).all()
    return jsonify([attempt.to_dict() for attempt in attempts])

@bp.route('/user/attempts/<int:attempt_id>', methods=['GET'])
@jwt_required()
def get_attempt_details(attempt_id):
    """Get comprehensive details of a specific exam attempt including answers and explanations"""
    from app.models.exam import QuestionWithOptions
    from app.models.user_progress import UserAnswer

    current_user_id = get_jwt_identity()

    attempt = ExamAttempt.query.get_or_404(attempt_id)

    if attempt.user_id != current_user_id:
        return jsonify({'error': 'Unauthorized access to this attempt'}), 403

    # Get all user answers for this attempt
    user_answers = UserAnswer.query.filter_by(attempt_id=attempt_id).all()

    # Build detailed answer information
    answer_details = []
    for user_answer in user_answers:
        question = QuestionWithOptions.query.get(user_answer.question_id)
        if question:
            answer_details.append({
                'question_id': question.id,
                'question_text': question.text,
                'selected_option': user_answer.selected_option,
                'correct_option': question.correct_option,
                'is_correct': user_answer.selected_option == question.correct_option,
                'explanation': question.explanation,
                'time_spent_seconds': user_answer.time_spent_seconds,
                'options': {
                    'A': question.option_a,
                    'B': question.option_b,
                    'C': question.option_c,
                    'D': question.option_d
                },
                'difficulty': question.difficulty
            })

    # Calculate additional metrics
    total_time_spent = sum(answer.time_spent_seconds for answer in user_answers)
    avg_time_per_question = total_time_spent / len(user_answers) if user_answers else 0
    percentage = (attempt.correct_answers / attempt.total_questions * 100) if attempt.total_questions > 0 else 0

    # Get purchase info for marks calculation
    purchase = Purchase.query.get(attempt.purchase_id)
    marks_per_question = purchase.marks if purchase else 4
    negative_marks = purchase.negative_marks if purchase else 1

    result = attempt.to_dict()
    result.update({
        'answer_details': answer_details,
        'percentage': round(percentage, 2),
        'avg_time_per_question': round(avg_time_per_question, 2),
        'total_time_spent_on_questions': total_time_spent,
        'marks_per_question': marks_per_question,
        'negative_marks': negative_marks,
        'performance_metrics': {
            'accuracy': round(percentage, 2),
            'speed_score': round(calculate_speed_score(avg_time_per_question), 2),
            'efficiency_score': round(calculate_efficiency_score(attempt.correct_answers, total_time_spent), 2)
        },
        'subject_performance': calculate_subject_performance(answer_details)
    })

    return jsonify(result)

def calculate_subject_performance(answer_details):
    """Calculate performance metrics by difficulty level"""
    difficulty_stats = {'easy': {'correct': 0, 'total': 0},
                       'medium': {'correct': 0, 'total': 0},
                       'hard': {'correct': 0, 'total': 0}}

    for answer in answer_details:
        difficulty = answer.get('difficulty', 'medium')
        if difficulty in difficulty_stats:
            difficulty_stats[difficulty]['total'] += 1
            if answer['is_correct']:
                difficulty_stats[difficulty]['correct'] += 1

    # Calculate percentages
    for difficulty in difficulty_stats:
        total = difficulty_stats[difficulty]['total']
        if total > 0:
            difficulty_stats[difficulty]['percentage'] = round(
                (difficulty_stats[difficulty]['correct'] / total) * 100, 2
            )
        else:
            difficulty_stats[difficulty]['percentage'] = 0

    return difficulty_stats

@bp.route('/user/performance-summary', methods=['GET'])
@jwt_required()
def get_user_performance_summary():
    """Get comprehensive performance summary for the current user"""
    from app.models.exam import Subject

    current_user_id = get_jwt_identity()

    # Get all attempts by the user
    attempts = ExamAttempt.query.filter_by(user_id=current_user_id).filter(
        ExamAttempt.completed_at.isnot(None)
    ).all()

    if not attempts:
        return jsonify({
            'total_exams': 0,
            'total_questions_attempted': 0,
            'overall_accuracy': 0,
            'average_score': 0,
            'total_time_spent': 0,
            'subject_wise_performance': {},
            'recent_attempts': [],
            'performance_trend': []
        })

    # Calculate overall statistics
    total_questions = sum(attempt.total_questions for attempt in attempts)
    total_correct = sum(attempt.correct_answers for attempt in attempts)
    total_wrong = sum(attempt.wrong_answers for attempt in attempts)
    total_time = sum(attempt.time_taken_seconds for attempt in attempts)
    total_score = sum(attempt.score for attempt in attempts)

    overall_accuracy = (total_correct / total_questions * 100) if total_questions > 0 else 0
    average_score = total_score / len(attempts) if attempts else 0

    # Subject-wise performance
    subject_performance = {}
    for attempt in attempts:
        purchase = Purchase.query.get(attempt.purchase_id)
        if purchase:
            subject = Subject.query.get(purchase.subject_id)
            if subject:
                subject_name = subject.subject_name
                if subject_name not in subject_performance:
                    subject_performance[subject_name] = {
                        'attempts': 0,
                        'total_questions': 0,
                        'correct_answers': 0,
                        'total_score': 0,
                        'best_score': 0,
                        'latest_attempt': None
                    }

                subject_performance[subject_name]['attempts'] += 1
                subject_performance[subject_name]['total_questions'] += attempt.total_questions
                subject_performance[subject_name]['correct_answers'] += attempt.correct_answers
                subject_performance[subject_name]['total_score'] += attempt.score
                subject_performance[subject_name]['best_score'] = max(
                    subject_performance[subject_name]['best_score'],
                    attempt.score
                )
                subject_performance[subject_name]['latest_attempt'] = attempt.completed_at.isoformat()

    # Calculate accuracy for each subject
    for subject_name in subject_performance:
        perf = subject_performance[subject_name]
        perf['accuracy'] = (perf['correct_answers'] / perf['total_questions'] * 100) if perf['total_questions'] > 0 else 0
        perf['average_score'] = perf['total_score'] / perf['attempts'] if perf['attempts'] > 0 else 0

    # Recent attempts (last 5)
    recent_attempts = sorted(attempts, key=lambda x: x.completed_at, reverse=True)[:5]
    recent_attempts_data = []
    for attempt in recent_attempts:
        purchase = Purchase.query.get(attempt.purchase_id)
        subject_name = "Unknown"
        if purchase:
            subject = Subject.query.get(purchase.subject_id)
            if subject:
                subject_name = subject.subject_name

        recent_attempts_data.append({
            'id': attempt.id,
            'subject': subject_name,
            'score': attempt.score,
            'total_questions': attempt.total_questions,
            'correct_answers': attempt.correct_answers,
            'accuracy': (attempt.correct_answers / attempt.total_questions * 100) if attempt.total_questions > 0 else 0,
            'completed_at': attempt.completed_at.isoformat(),
            'time_taken_seconds': attempt.time_taken_seconds
        })

    # Performance trend (last 10 attempts)
    trend_attempts = sorted(attempts, key=lambda x: x.completed_at)[-10:]
    performance_trend = []
    for i, attempt in enumerate(trend_attempts):
        accuracy = (attempt.correct_answers / attempt.total_questions * 100) if attempt.total_questions > 0 else 0
        performance_trend.append({
            'attempt_number': i + 1,
            'accuracy': round(accuracy, 2),
            'score': attempt.score,
            'date': attempt.completed_at.isoformat()
        })

    return jsonify({
        'total_exams': len(attempts),
        'total_questions_attempted': total_questions,
        'overall_accuracy': round(overall_accuracy, 2),
        'average_score': round(average_score, 2),
        'total_time_spent': total_time,
        'total_correct_answers': total_correct,
        'total_wrong_answers': total_wrong,
        'subject_wise_performance': subject_performance,
        'recent_attempts': recent_attempts_data,
        'performance_trend': performance_trend
    })

@bp.route('/user/purchases/<int:purchase_id>/attempts', methods=['GET'])
@jwt_required()
def get_purchase_attempts(purchase_id):
    """Get all attempts for a specific purchase"""
    current_user_id = get_jwt_identity()

    # Check if purchase exists and belongs to the current user
    purchase = Purchase.query.get_or_404(purchase_id)

    if purchase.user_id != current_user_id:
        return jsonify({'error': 'Unauthorized access to this purchase'}), 403

    attempts = ExamAttempt.query.filter_by(purchase_id=purchase_id).all()
    return jsonify([attempt.to_dict() for attempt in attempts])
