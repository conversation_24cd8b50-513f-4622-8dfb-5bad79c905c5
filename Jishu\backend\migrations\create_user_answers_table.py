"""Migration to create user_answers table for detailed exam scoring"""

import os
import sys
import pymysql
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Add the parent directory to sys.path to allow importing the app module
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Use PyMySQL as the MySQL client
pymysql.install_as_MySQLdb()

# Import Flask app and db after setting up the path
from app import create_app, db

def upgrade():
    """Create user_answers table for detailed exam scoring"""
    try:
        # Create the Flask app
        app = create_app()

        # Push an application context
        with app.app_context():
            logger.info("Starting user_answers table migration...")

            # Create user_answers table
            logger.info("Creating user_answers table...")
            db.engine.execute('''
            CREATE TABLE IF NOT EXISTS user_answers (
                id INT AUTO_INCREMENT PRIMARY KEY,
                attempt_id INT NOT NULL,
                question_id INT NOT NULL,
                selected_option VARCHAR(1),
                is_marked_for_review BOOLEAN DEFAULT FALSE,
                time_spent_seconds INT DEFAULT 0,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (attempt_id) REFERENCES exam_attempts(id) ON DELETE CASCADE,
                FOREIGN KEY (question_id) REFERENCES questions_with_options(id),
                UNIQUE KEY unique_attempt_question (attempt_id, question_id),
                INDEX idx_user_answers_attempt_id (attempt_id)
            )
            ''')

            logger.info("user_answers table created successfully!")

            # Verify the table was created
            result = db.engine.execute("SHOW TABLES LIKE 'user_answers'")
            if result.fetchone():
                logger.info("✓ user_answers table exists in database")
            else:
                logger.error("✗ user_answers table was not created")

            logger.info("Migration completed successfully!")
    except Exception as e:
        logger.error(f"Migration failed: {str(e)}")
        raise

def downgrade():
    """Drop user_answers table"""
    try:
        # Create the Flask app
        app = create_app()

        # Push an application context
        with app.app_context():
            logger.info("Starting user_answers table downgrade...")

            # Drop user_answers table
            logger.info("Dropping user_answers table...")
            db.engine.execute('DROP TABLE IF EXISTS user_answers')

            logger.info("user_answers table dropped successfully!")
            logger.info("Downgrade completed successfully!")
    except Exception as e:
        logger.error(f"Downgrade failed: {str(e)}")
        raise

if __name__ == "__main__":
    upgrade()
