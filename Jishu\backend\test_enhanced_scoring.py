#!/usr/bin/env python3
"""Test script for enhanced scoring system"""

import requests
import json
import sys
import os

# Add the current directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Test configuration
BASE_URL = "http://localhost:5000"
TEST_USER_EMAIL = "<EMAIL>"
TEST_USER_PASSWORD = "password123"

def test_enhanced_scoring():
    """Test the enhanced scoring system"""
    print("🧪 Testing Enhanced Scoring System")
    print("=" * 50)
    
    # Step 1: Login to get JWT token
    print("1. Logging in...")
    login_response = requests.post(f"{BASE_URL}/api/auth/login", json={
        "email": TEST_USER_EMAIL,
        "password": TEST_USER_PASSWORD
    })
    
    if login_response.status_code != 200:
        print(f"❌ Login failed: {login_response.status_code}")
        print(f"Response: {login_response.text}")
        return False
    
    token = login_response.json().get('access_token')
    headers = {'Authorization': f'Bearer {token}'}
    print("✅ Login successful")
    
    # Step 2: Test performance summary endpoint
    print("\n2. Testing performance summary endpoint...")
    perf_response = requests.get(f"{BASE_URL}/api/user/performance-summary", headers=headers)
    
    if perf_response.status_code == 200:
        perf_data = perf_response.json()
        print("✅ Performance summary retrieved successfully")
        print(f"   Total exams: {perf_data.get('total_exams', 0)}")
        print(f"   Overall accuracy: {perf_data.get('overall_accuracy', 0)}%")
        print(f"   Average score: {perf_data.get('average_score', 0)}")
        print(f"   Total questions attempted: {perf_data.get('total_questions_attempted', 0)}")
    else:
        print(f"⚠️  Performance summary endpoint returned: {perf_response.status_code}")
        print(f"Response: {perf_response.text}")
    
    # Step 3: Get user's exam attempts
    print("\n3. Getting user's exam attempts...")
    attempts_response = requests.get(f"{BASE_URL}/api/user/attempts", headers=headers)
    
    if attempts_response.status_code == 200:
        attempts = attempts_response.json()
        print(f"✅ Found {len(attempts)} exam attempts")
        
        if attempts:
            # Test detailed results for the first attempt
            first_attempt = attempts[0]
            attempt_id = first_attempt['id']
            
            print(f"\n4. Testing detailed results for attempt {attempt_id}...")
            details_response = requests.get(f"{BASE_URL}/api/user/attempts/{attempt_id}", headers=headers)
            
            if details_response.status_code == 200:
                details = details_response.json()
                print("✅ Detailed results retrieved successfully")
                print(f"   Score: {details.get('score', 0)}")
                print(f"   Percentage: {details.get('percentage', 0)}%")
                print(f"   Correct answers: {details.get('correct_answers', 0)}")
                print(f"   Wrong answers: {details.get('wrong_answers', 0)}")
                print(f"   Answer details count: {len(details.get('answer_details', []))}")
                
                # Check performance metrics
                metrics = details.get('performance_metrics', {})
                if metrics:
                    print(f"   Performance metrics:")
                    print(f"     - Accuracy: {metrics.get('accuracy', 0)}%")
                    print(f"     - Speed score: {metrics.get('speed_score', 0)}%")
                    print(f"     - Efficiency score: {metrics.get('efficiency_score', 0)}%")
            else:
                print(f"❌ Failed to get detailed results: {details_response.status_code}")
                print(f"Response: {details_response.text}")
        else:
            print("ℹ️  No exam attempts found for this user")
    else:
        print(f"❌ Failed to get exam attempts: {attempts_response.status_code}")
        print(f"Response: {attempts_response.text}")
    
    print("\n" + "=" * 50)
    print("🎉 Enhanced scoring system test completed!")
    return True

if __name__ == "__main__":
    try:
        test_enhanced_scoring()
    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")
        sys.exit(1)
