import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  ActivityIndicator,
  Alert
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import { examAttemptsAPI } from '../services/api';

const COLORS = {
  primary: '#3461eb',
  secondary: '#6200ee',
  background: '#F9FAFA',
  card: '#FFFFFF',
  text: '#1C1D22',
  textSecondary: '#3C3F4A',
  border: '#EEEFF2',
  success: '#4CAF50',
  warning: '#FFC107',
  error: '#F44336',
  correct: '#4CAF50',
  incorrect: '#F44336',
  unattempted: '#9E9E9E'
};

const DetailedResultsScreen = ({ route }) => {
  const { attemptId, examTitle } = route.params;
  const navigation = useNavigation();
  const [loading, setLoading] = useState(true);
  const [results, setResults] = useState(null);
  const [error, setError] = useState(null);
  const [selectedTab, setSelectedTab] = useState('overview'); // overview, questions, analysis

  useEffect(() => {
    fetchDetailedResults();
  }, [attemptId]);

  const fetchDetailedResults = async () => {
    try {
      setLoading(true);
      const response = await examAttemptsAPI.getAttemptDetails(attemptId);
      setResults(response);
      setError(null);
    } catch (err) {
      console.error('Error fetching detailed results:', err);
      setError('Failed to load detailed results');
    } finally {
      setLoading(false);
    }
  };

  const formatTime = (seconds) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds < 10 ? '0' : ''}${remainingSeconds}`;
  };

  const getOptionIcon = (questionData, option) => {
    const isSelected = questionData.selected_option === option;
    const isCorrect = questionData.correct_option === option;
    
    if (isSelected && isCorrect) {
      return <MaterialIcons name="check-circle" size={20} color={COLORS.correct} />;
    } else if (isSelected && !isCorrect) {
      return <MaterialIcons name="cancel" size={20} color={COLORS.error} />;
    } else if (!isSelected && isCorrect) {
      return <MaterialIcons name="radio-button-unchecked" size={20} color={COLORS.correct} />;
    } else {
      return <MaterialIcons name="radio-button-unchecked" size={20} color={COLORS.textSecondary} />;
    }
  };

  const renderOverviewTab = () => (
    <ScrollView style={styles.tabContent}>
      {/* Score Summary */}
      <View style={styles.summaryCard}>
        <Text style={styles.cardTitle}>Exam Summary</Text>
        <View style={styles.scoreRow}>
          <Text style={styles.scoreLabel}>Final Score:</Text>
          <Text style={styles.scoreValue}>
            {results.score} ({results.percentage}%)
          </Text>
        </View>
        
        <View style={styles.statsGrid}>
          <View style={styles.statBox}>
            <Text style={[styles.statNumber, { color: COLORS.correct }]}>
              {results.correct_answers}
            </Text>
            <Text style={styles.statLabel}>Correct</Text>
          </View>
          <View style={styles.statBox}>
            <Text style={[styles.statNumber, { color: COLORS.error }]}>
              {results.wrong_answers}
            </Text>
            <Text style={styles.statLabel}>Wrong</Text>
          </View>
          <View style={styles.statBox}>
            <Text style={[styles.statNumber, { color: COLORS.unattempted }]}>
              {results.unattempted}
            </Text>
            <Text style={styles.statLabel}>Skipped</Text>
          </View>
          <View style={styles.statBox}>
            <Text style={styles.statNumber}>
              {formatTime(results.time_taken_seconds)}
            </Text>
            <Text style={styles.statLabel}>Time</Text>
          </View>
        </View>
      </View>

      {/* Performance Metrics */}
      {results.performance_metrics && (
        <View style={styles.summaryCard}>
          <Text style={styles.cardTitle}>Performance Analysis</Text>
          <View style={styles.metricsGrid}>
            <View style={styles.metricItem}>
              <Text style={styles.metricValue}>
                {Math.round(results.performance_metrics.accuracy)}%
              </Text>
              <Text style={styles.metricLabel}>Accuracy</Text>
            </View>
            <View style={styles.metricItem}>
              <Text style={styles.metricValue}>
                {Math.round(results.performance_metrics.speed_score)}%
              </Text>
              <Text style={styles.metricLabel}>Speed Score</Text>
            </View>
            <View style={styles.metricItem}>
              <Text style={styles.metricValue}>
                {Math.round(results.performance_metrics.efficiency_score)}%
              </Text>
              <Text style={styles.metricLabel}>Efficiency</Text>
            </View>
            <View style={styles.metricItem}>
              <Text style={styles.metricValue}>
                {Math.round(results.avg_time_per_question)}s
              </Text>
              <Text style={styles.metricLabel}>Avg Time/Q</Text>
            </View>
          </View>
        </View>
      )}

      {/* Subject Performance */}
      {results.subject_performance && (
        <View style={styles.summaryCard}>
          <Text style={styles.cardTitle}>Performance by Difficulty</Text>
          {Object.entries(results.subject_performance).map(([difficulty, stats]) => (
            <View key={difficulty} style={styles.difficultyRow}>
              <Text style={styles.difficultyLabel}>
                {difficulty.charAt(0).toUpperCase() + difficulty.slice(1)}:
              </Text>
              <Text style={styles.difficultyStats}>
                {stats.correct}/{stats.total} ({stats.percentage}%)
              </Text>
            </View>
          ))}
        </View>
      )}
    </ScrollView>
  );

  const renderQuestionsTab = () => (
    <ScrollView style={styles.tabContent}>
      {results.answer_details?.map((questionData, index) => (
        <View key={questionData.question_id} style={styles.questionCard}>
          <View style={styles.questionHeader}>
            <Text style={styles.questionNumber}>Q{index + 1}</Text>
            <View style={styles.questionStatus}>
              {questionData.is_correct ? (
                <MaterialIcons name="check-circle" size={20} color={COLORS.correct} />
              ) : (
                <MaterialIcons name="cancel" size={20} color={COLORS.error} />
              )}
            </View>
          </View>
          
          <Text style={styles.questionText}>{questionData.question_text}</Text>
          
          <View style={styles.optionsContainer}>
            {Object.entries(questionData.options).map(([option, text]) => (
              <View key={option} style={styles.optionRow}>
                {getOptionIcon(questionData, option)}
                <Text style={[
                  styles.optionText,
                  questionData.selected_option === option && styles.selectedOptionText,
                  questionData.correct_option === option && styles.correctOptionText
                ]}>
                  {option}. {text}
                </Text>
              </View>
            ))}
          </View>
          
          {questionData.explanation && (
            <View style={styles.explanationContainer}>
              <Text style={styles.explanationTitle}>Explanation:</Text>
              <Text style={styles.explanationText}>{questionData.explanation}</Text>
            </View>
          )}
          
          <View style={styles.questionFooter}>
            <Text style={styles.timeSpent}>
              Time: {formatTime(questionData.time_spent_seconds)}
            </Text>
            <Text style={[
              styles.difficultyBadge,
              { backgroundColor: getDifficultyColor(questionData.difficulty) }
            ]}>
              {questionData.difficulty}
            </Text>
          </View>
        </View>
      ))}
    </ScrollView>
  );

  const getDifficultyColor = (difficulty) => {
    switch (difficulty?.toLowerCase()) {
      case 'easy': return '#4CAF50';
      case 'medium': return '#FFC107';
      case 'hard': return '#F44336';
      default: return '#9E9E9E';
    }
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={COLORS.primary} />
        <Text style={styles.loadingText}>Loading detailed results...</Text>
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.errorContainer}>
        <MaterialIcons name="error-outline" size={48} color={COLORS.error} />
        <Text style={styles.errorText}>{error}</Text>
        <TouchableOpacity style={styles.retryButton} onPress={fetchDetailedResults}>
          <Text style={styles.retryButtonText}>Retry</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <MaterialIcons name="arrow-back" size={24} color={COLORS.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>{examTitle || 'Exam Results'}</Text>
        <View style={{ width: 24 }} />
      </View>

      {/* Tab Navigation */}
      <View style={styles.tabContainer}>
        <TouchableOpacity
          style={[styles.tab, selectedTab === 'overview' && styles.activeTab]}
          onPress={() => setSelectedTab('overview')}
        >
          <Text style={[styles.tabText, selectedTab === 'overview' && styles.activeTabText]}>
            Overview
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.tab, selectedTab === 'questions' && styles.activeTab]}
          onPress={() => setSelectedTab('questions')}
        >
          <Text style={[styles.tabText, selectedTab === 'questions' && styles.activeTabText]}>
            Questions
          </Text>
        </TouchableOpacity>
      </View>

      {/* Tab Content */}
      {selectedTab === 'overview' ? renderOverviewTab() : renderQuestionsTab()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: COLORS.card,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.border,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: COLORS.text,
  },
  tabContainer: {
    flexDirection: 'row',
    backgroundColor: COLORS.card,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.border,
  },
  tab: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
  },
  activeTab: {
    borderBottomWidth: 2,
    borderBottomColor: COLORS.primary,
  },
  tabText: {
    fontSize: 16,
    color: COLORS.textSecondary,
  },
  activeTabText: {
    color: COLORS.primary,
    fontWeight: 'bold',
  },
  tabContent: {
    flex: 1,
    padding: 16,
  },
  summaryCard: {
    backgroundColor: COLORS.card,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: COLORS.text,
    marginBottom: 16,
  },
  scoreRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  scoreLabel: {
    fontSize: 16,
    color: COLORS.textSecondary,
  },
  scoreValue: {
    fontSize: 20,
    fontWeight: 'bold',
    color: COLORS.primary,
  },
  statsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  statBox: {
    alignItems: 'center',
    flex: 1,
  },
  statNumber: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: COLORS.textSecondary,
  },
  metricsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  metricItem: {
    width: '48%',
    alignItems: 'center',
    marginBottom: 12,
    backgroundColor: COLORS.background,
    borderRadius: 8,
    padding: 12,
  },
  metricValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: COLORS.primary,
    marginBottom: 4,
  },
  metricLabel: {
    fontSize: 12,
    color: COLORS.textSecondary,
  },
  difficultyRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  difficultyLabel: {
    fontSize: 14,
    color: COLORS.textSecondary,
  },
  difficultyStats: {
    fontSize: 14,
    fontWeight: 'bold',
    color: COLORS.text,
  },
  questionCard: {
    backgroundColor: COLORS.card,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  questionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  questionNumber: {
    fontSize: 16,
    fontWeight: 'bold',
    color: COLORS.primary,
  },
  questionStatus: {
    // Icon container
  },
  questionText: {
    fontSize: 16,
    color: COLORS.text,
    marginBottom: 16,
    lineHeight: 24,
  },
  optionsContainer: {
    marginBottom: 16,
  },
  optionRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  optionText: {
    fontSize: 14,
    color: COLORS.text,
    marginLeft: 8,
    flex: 1,
  },
  selectedOptionText: {
    fontWeight: 'bold',
  },
  correctOptionText: {
    color: COLORS.correct,
  },
  explanationContainer: {
    backgroundColor: COLORS.background,
    borderRadius: 8,
    padding: 12,
    marginBottom: 12,
  },
  explanationTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: COLORS.text,
    marginBottom: 4,
  },
  explanationText: {
    fontSize: 14,
    color: COLORS.textSecondary,
    lineHeight: 20,
  },
  questionFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  timeSpent: {
    fontSize: 12,
    color: COLORS.textSecondary,
  },
  difficultyBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    fontSize: 12,
    color: '#fff',
    fontWeight: 'bold',
    textTransform: 'capitalize',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: COLORS.textSecondary,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  errorText: {
    marginTop: 12,
    fontSize: 16,
    color: COLORS.error,
    textAlign: 'center',
  },
  retryButton: {
    marginTop: 16,
    backgroundColor: COLORS.primary,
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
  },
  retryButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default DetailedResultsScreen;
