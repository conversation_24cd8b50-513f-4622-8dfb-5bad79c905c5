import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  FlatList,
  StyleSheet,
  TouchableOpacity,
  SafeAreaView,
  ScrollView,
  ActivityIndicator,
  Animated,
  RefreshControl,
  Alert
} from 'react-native';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import { useNavigation } from '@react-navigation/native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useSelector } from 'react-redux';
import { subjectsAPI, examCategoriesAPI, userExamsAPI } from '../services/api';

// Define consistent colors
const COLORS = {
  primary: '#3461eb',
  secondary: '#6200ee',
  background: '#F9FAFA',
  card: '#FFFFFF',
  text: '#1C1D22',
  textSecondary: '#3C3F4A',
  border: '#EEEFF2',
  success: '#4CAF50',
  warning: '#FFC107',
  error: '#F44336'
};

// Define consistent fonts
const FONTS = {
  regular: {
    fontFamily: 'System',
    fontWeight: 'normal',
  },
  medium: {
    fontFamily: 'System',
    fontWeight: '500',
  },
  bold: {
    fontFamily: 'System',
    fontWeight: 'bold',
  }
};

const FullExamListScreen = ({ route }) => {
  const navigation = useNavigation();
  const { user, isAuthenticated } = useSelector((state) => state.auth);
  const [subscribedExams, setSubscribedExams] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const fadeAnim = useState(new Animated.Value(0))[0];
  const [selectedCategory, setSelectedCategory] = useState(null);
  const [subjects, setSubjects] = useState([]);
  const [loadingSubjects, setLoadingSubjects] = useState(false);
  const [examCategories, setExamCategories] = useState([]);
  const [loadingCategories, setLoadingCategories] = useState(true);
  const [error, setError] = useState(null);


  useEffect(() => {
    console.log('FullExamListScreen useEffect running');
    const loadInitialData = async () => {
      try {
        setLoading(true);
        await Promise.all([
          loadSubscribedExams(),
          loadExamCategories()
        ]);

        // Check if we have a selected category from navigation params
        // Only process this if we're not coming directly from the bottom tab navigation
        const currentRoute = navigation.getState().routes[navigation.getState().index];
        const isFromBottomTab = currentRoute.name === 'Exams' && !route.params;

        if (!isFromBottomTab && route.params?.selectedCategoryId && route.params?.selectedCategoryName) {
          console.log('Found selected category in params:', route.params.selectedCategoryId, route.params.selectedCategoryName);
          handleCategoryPress(route.params.selectedCategoryId, route.params.selectedCategoryName);

          // Make sure the header is shown with the category name
          navigation.setOptions({
            headerShown: true,
            title: route.params.selectedCategoryName,
            headerLeft: () => (
              <TouchableOpacity
                style={styles.headerBackButton}
                onPress={() => {
                  resetToCategoriesView();
                }}
                activeOpacity={0.7}
              >
                <MaterialIcons name="arrow-back" size={24} color={COLORS.text} />
              </TouchableOpacity>
            )
          });
        }

        setLoading(false);
      } catch (error) {
        console.error('Error loading initial data:', error);
        setLoading(false);
      }
    };

    loadInitialData();
  }, [route.params]);

  // Refresh data when the screen comes into focus
  useEffect(() => {
    const unsubscribe = navigation.addListener('focus', () => {
      console.log('FullExamListScreen focused, refreshing data...');

      // Check if we're coming from the bottom tab navigation
      // If the route name is 'Exams', we're likely coming from the bottom tab
      const currentRoute = navigation.getState().routes[navigation.getState().index];
      const isFromBottomTab = currentRoute.name === 'Exams' && !route.params;

      if (isFromBottomTab) {
        console.log('Coming from bottom tab navigation, resetting to categories view');
        // Reset to categories view
        resetToCategoriesView();
      }

      // Refresh the exam categories data
      loadExamCategories();
    });

    return unsubscribe;
  }, [navigation]);

  // Set initial screen title and configure header
  useEffect(() => {
    // Only set the title if we don't have a selected category
    if (!selectedCategory) {
      navigation.setOptions({
        title: 'Exam Categories',
        headerShown: true,
        headerLeft: undefined // Use default back button if needed
      });
    }
  }, [selectedCategory, navigation]);

  // Function to fetch user's purchased exams - same as DashboardScreen
  const fetchUserExams = async () => {
    try {
      console.log('Fetching exams for user:', user?.id, user?.name);
      const response = await userExamsAPI.getAll();
      console.log('User exams fetched:', response);

      // Check if response is an array
      if (Array.isArray(response) && response.length > 0) {
        // Format the data to match our component's expected structure
        const formattedExams = response.map(exam => {
          // Get the latest attempt if any
          const latestAttempt = exam.attempts && exam.attempts.length > 0
            ? exam.attempts.sort((a, b) => new Date(b.completed_at) - new Date(a.completed_at))[0]
            : null;

          // Get category name from the subject or from the category_id
          let categoryName = '';
          if (exam.subject && exam.subject.category_name) {
            categoryName = exam.subject.category_name;
          } else if (exam.category_name) {
            categoryName = exam.category_name;
          } else if (exam.category_id) {
            // Map category ID to name if needed
            const categoryMap = {
              '1': 'NEET',
              '2': 'JEE',
              '3': 'GATE',
              'NEET': 'NEET',
              'JEE': 'JEE',
              'GATE': 'GATE'
            };
            categoryName = categoryMap[exam.category_id] || exam.category_id;
          }

          // Format the title to include category name for full mock tests
          let title = exam.subject.name;
          if (title.includes('Full Mock Test')) {
            // Title already has the format we want
          } else if (exam.subject_id.toString().startsWith('full_mock_')) {
            // Format as "Category Full Mock Test"
            title = `${categoryName} Full Mock Test`;
          }

          return {
            id: exam.id.toString(),
            title: title,
            description: exam.subject.description || 'Take this test to improve your skills',
            score: latestAttempt ? latestAttempt.score : 0,
            totalQuestions: latestAttempt ? latestAttempt.total_questions : 0,
            attemptCount: exam.attempts ? exam.attempts.length : 0,
            lastAttemptDate: latestAttempt ? latestAttempt.completed_at : exam.purchased_at,
            subjectId: exam.subject_id,
            userExamId: exam.id,
            user_id: exam.user_id,
            category: categoryName,
            categoryName: categoryName,
            category_id: exam.category_id || (exam.subject && exam.subject.category_id)
          };
        });

        console.log(`Found ${formattedExams.length} exams for user ${user?.id}`);
        setSubscribedExams(formattedExams);
        return formattedExams;
      } else {
        console.log('No user exams found or invalid response format');
        setSubscribedExams([]);
        return [];
      }
    } catch (error) {
      console.error('Error fetching user exams:', error);
      // If we can't fetch from API, try to get from local storage as fallback
      try {
        const storedExams = await AsyncStorage.getItem('subscribedExams');
        if (storedExams) {
          const parsedExams = JSON.parse(storedExams);
          setSubscribedExams(parsedExams);
          return parsedExams;
        }
      } catch (storageError) {
        console.error('Error reading from storage:', storageError);
      }

      setSubscribedExams([]);
      setError('Failed to load your purchased exams. Please try again.');
      return [];
    }
  };

  const loadSubscribedExams = async () => {
    try {
      setLoading(true);
      // Use the new fetchUserExams function if user is authenticated
      if (isAuthenticated && user) {
        await fetchUserExams();
      } else {
        // Fallback to AsyncStorage for non-authenticated users
        const storedExams = await AsyncStorage.getItem('subscribedExams');
        if (storedExams) {
          setSubscribedExams(JSON.parse(storedExams));
        } else {
          setSubscribedExams([]);
        }
      }
      setLoading(false);

      // Fade in animation
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 500,
        useNativeDriver: true
      }).start();

    } catch (error) {
      console.error('Error loading subscribed exams:', error);
      setSubscribedExams([]);
      setLoading(false);
    }
  };

  // Function to fetch exam categories from the backend - same as Dashboard
  const loadExamCategories = async () => {
    try {
      setLoadingCategories(true);
      console.log('Fetching exam categories...');
      const response = await examCategoriesAPI.getAll();
      console.log('Exam categories loaded:', response);

      // Check if response is an array
      if (Array.isArray(response)) {
        console.log(`Found ${response.length} exam categories`);
        setExamCategories(response);
      } else {
        console.log('Invalid response format, setting empty array');
        setExamCategories([]);
      }

      setLoadingCategories(false);
      return response;
    } catch (error) {
      console.error('Error loading exam categories:', error);
      setExamCategories([]);
      setLoadingCategories(false);
      return [];
    }
  };

  // getCategoryIcon function removed as it's no longer needed

  // CategoryCard component for grid layout
  const CategoryCard = ({ title, onPress, category }) => {
    // Get icon based on category name
    const getIconComponent = () => {
      switch (title.toLowerCase()) {
        case 'neet':
        case 'medicine':
        case 'medical':
          return (
            <View style={[styles.categoryIcon, { backgroundColor: '#FFF9E6' }]}>
              <MaterialCommunityIcons name="stethoscope" size={32} color="#FF9500" />
            </View>
          );
        case 'jee':
        case 'jee mains':
        case 'jee advanced':
        case 'engineering':
        case 'chemistry':
          return (
            <View style={[styles.categoryIcon, { backgroundColor: '#E6F9F0' }]}>
              <MaterialCommunityIcons name="flask" size={32} color="#4CAF50" />
            </View>
          );
        case 'gate':
        case 'biology':
          return (
            <View style={[styles.categoryIcon, { backgroundColor: '#E6F0FF' }]}>
              <MaterialCommunityIcons name="dna" size={32} color="#2196F3" />
            </View>
          );
        case 'cet':
        case 'mathematics':
        case 'math':
          return (
            <View style={[styles.categoryIcon, { backgroundColor: '#FFE6E6' }]}>
              <MaterialCommunityIcons name="chart-pie" size={32} color="#F44336" />
            </View>
          );
        case 'language':
        case 'english':
          return (
            <View style={[styles.categoryIcon, { backgroundColor: '#E6E6FF' }]}>
              <MaterialCommunityIcons name="translate" size={32} color="#673AB7" />
            </View>
          );
        default:
          return (
            <View style={[styles.categoryIcon, { backgroundColor: '#F0F0F0' }]}>
              <MaterialCommunityIcons name="school" size={32} color="#3461eb" />
            </View>
          );
      }
    };

    // Get pricing information from category subjects
    const getPricingInfo = () => {
      if (!category || !category.subjects || category.subjects.length === 0) {
        return { minPrice: 'Free', maxPrice: 'Free', currency: 'INR' };
      }

      const prices = category.subjects
        .filter(subject => subject.price && subject.price > 0)
        .map(subject => subject.price);

      if (prices.length === 0) {
        return { minPrice: 'Free', maxPrice: 'Free', currency: 'INR' };
      }

      const minPrice = Math.min(...prices);
      const maxPrice = Math.max(...prices);
      const currency = category.subjects[0].currency || 'INR';

      if (minPrice === maxPrice) {
        return { minPrice: `₹${minPrice}`, maxPrice: null, currency };
      } else {
        return { minPrice: `₹${minPrice}`, maxPrice: `₹${maxPrice}`, currency };
      }
    };

    const pricingInfo = getPricingInfo();
    const priceDisplay = pricingInfo.maxPrice
      ? `${pricingInfo.minPrice} - ${pricingInfo.maxPrice}`
      : pricingInfo.minPrice;

    return (
      <TouchableOpacity style={styles.categoryCard} onPress={onPress} activeOpacity={0.7}>
        {getIconComponent()}
        <Text style={styles.categoryTitle}>{title}</Text>
        <Text style={styles.categoryPrice}>{priceDisplay}</Text>
        <Text style={styles.categorySubjects}>
          {category && category.subjects ? `${category.subjects.length} subjects` : 'Loading...'}
        </Text>
      </TouchableOpacity>
    );
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await Promise.all([loadSubscribedExams(), loadExamCategories()]);
    setRefreshing(false);
  };

  // Map subject names to appropriate icons
  const getIconForSubject = (subjectName) => {
    const name = subjectName.toLowerCase();
    if (name.includes('physics')) return 'atom';
    if (name.includes('chemistry')) return 'flask-empty-outline';
    if (name.includes('biology') || name.includes('botany')) return 'virus-outline';
    if (name.includes('zoology')) return 'dna';
    if (name.includes('math')) return 'calculator-variant-outline';
    if (name.includes('full') || name.includes('mock')) return 'stethoscope';
    if (name.includes('computer')) return 'laptop';
    if (name.includes('electronics')) return 'chip';
    return 'book-open-variant'; // Default icon
  };

  const fetchSubjectsForCategory = async (categoryId, categoryName) => {
    setLoadingSubjects(true);
    try {
      // Log the received categoryId for debugging
      console.log('Received categoryId:', categoryId, 'Type:', typeof categoryId, 'Category Name:', categoryName);

      // Ensure categoryId is valid and properly formatted
      let categoryIdParam;
      if (typeof categoryId === 'number') {
        categoryIdParam = String(categoryId);
      } else if (typeof categoryId === 'string' && !isNaN(Number(categoryId))) {
        categoryIdParam = categoryId;
      } else {
        console.warn('Invalid categoryId received:', categoryId);
        // Use a default category ID for testing
        categoryIdParam = '1';
      }

      console.log('Using categoryIdParam for API call:', categoryIdParam);

      // Use the getByCategory method which maps to our Flask API endpoint
      const response = await subjectsAPI.getByCategory(categoryIdParam);
      console.log('Subjects fetched:', response);

      // Check if response is an array
      if (Array.isArray(response)) {
        console.log(`Found ${response.length} subjects for category ${categoryName}`);

        if (response.length > 0) {
          // Format the subjects data
          const formattedSubjects = response.map(subject => ({
            id: subject.id,
            name: subject.name,
            description: subject.description || `${categoryName} ${subject.name}`,
            icon: getIconForSubject(subject.name),
            isFullMock: subject.is_full_mock || false,
            duration_minutes: subject.duration_minutes || 60,
            category_id: subject.category_id || categoryIdParam,
            categoryName: categoryName,
            price: subject.price || 0,
            currency: subject.currency || 'INR',
            formatted_price: subject.formatted_price || (subject.price ? `₹${subject.price}` : 'Free')
          }));

          // Add a Full Mock Exam option if not already present
          const hasMockExam = formattedSubjects.some(s => s.isFullMock);
          if (!hasMockExam && formattedSubjects.length > 0) {
            // Get the highest price from existing subjects for full mock pricing
            const maxPrice = Math.max(...formattedSubjects.map(s => s.price || 0));
            const mockPrice = maxPrice > 0 ? Math.round(maxPrice * 1.5) : 499; // 50% more than highest subject price

            formattedSubjects.push({
              id: 'full_mock',
              name: 'Full Mock Exam',
              description: `Complete ${categoryName} exam pattern`,
              icon: 'stethoscope',
              isFullMock: true,
              duration_minutes: 180, // Default 3 hours for full mock
              category_id: categoryIdParam,
              categoryName: categoryName,
              price: mockPrice,
              currency: 'INR',
              formatted_price: `₹${mockPrice}`
            });
          }

          setSubjects(formattedSubjects);
        } else {
          console.log('No subjects found for this category');
          setSubjects([]);
        }
      } else {
        console.log('Invalid response format, setting empty array');
        setSubjects([]);
      }

      setLoadingSubjects(false);
    } catch (err) {
      console.error('Error fetching subjects:', err);
      setSubjects([]);
      setLoadingSubjects(false);
    }
  };

  const handleCategoryPress = (category, categoryName) => {
    console.log('handleCategoryPress called with:', category, categoryName);

    // Ensure we have a valid category ID
    const categoryId = typeof category === 'object' ? category.id : category;

    // Set the selected category state
    setSelectedCategory({
      id: categoryId,
      name: categoryName
    });

    // Update the navigation header title with consistent back button
    navigation.setOptions({
      headerShown: true,
      title: categoryName,
      headerLeft: () => (
        <TouchableOpacity
          style={styles.headerBackButton}
          onPress={() => {
            resetToCategoriesView();
          }}
          activeOpacity={0.7}
        >
          <MaterialIcons name="arrow-back" size={24} color={COLORS.text} />
        </TouchableOpacity>
      )
    });

    // Fetch subjects for the selected category
    fetchSubjectsForCategory(categoryId, categoryName);
  };

  // Function to reset to categories view
  const resetToCategoriesView = () => {
    console.log('Resetting to categories view');
    setSelectedCategory(null);
    setSubjects([]);
    navigation.setOptions({
      title: 'Exam Categories',
      headerLeft: undefined // Reset to default back button
    });
  };

  const handleSubjectPress = (subject) => {
    if (!selectedCategory) {
      Alert.alert('Error', 'No category selected. Please go back and select a category first.');
      return;
    }

    // Validate subject data
    if (!subject || !subject.id || !subject.name) {
      Alert.alert('Error', 'Invalid subject data. Please try again.');
      return;
    }

    // Determine question count based on subject type
    let questionCount = 50; // Default for subject-wise tests
    let durationMinutes = subject.duration_minutes || 60;

    // Adjust for full mock tests
    if (subject.isFullMock) {
      if (selectedCategory.name === 'NEET') {
        questionCount = 180;
        durationMinutes = 180; // 3 hours
      } else if (selectedCategory.name === 'JEE') {
        questionCount = 90;
        durationMinutes = 180; // 3 hours
      } else if (selectedCategory.name === 'GATE') {
        questionCount = 65;
        durationMinutes = 180; // 3 hours
      } else {
        questionCount = 100;
        durationMinutes = 120; // 2 hours
      }
    }

    // Duration formatting is now handled in TestInstructions screen

    // Navigate to test instructions with proper parameters
    console.log('Navigating to TestInstructions with subject:', subject.id, subject.name);
    navigation.navigate('TestInstructions', {
      subjectId: subject.id,
      subjectName: subject.name,
      categoryId: selectedCategory.id,
      categoryName: selectedCategory.name,
      isFullMock: subject.isFullMock || false,
      questionCount: questionCount,
      durationMinutes: durationMinutes,
      price: subject.price || 0,
      currency: subject.currency || 'INR',
      formattedPrice: subject.formatted_price || (subject.price ? `₹${subject.price}` : 'Free')
    });
  };

  // We don't need these functions anymore as we're navigating directly to subject selection
  // Keeping the code commented for reference in case we need to revert
  /*
  const isExamSubscribed = (examId) => {
    return subscribedExams.some(exam => exam.id === examId);
  };

  const getExamScore = (examId) => {
    const exam = subscribedExams.find(exam => exam.id === examId);
    return exam ? `${exam.score}/${exam.totalQuestions}` : null;
  };

  const handleExamPress = (exam) => {
    const isSubscribed = isExamSubscribed(exam.id);

    if (isSubscribed) {
      // Navigate to exam questions - no limit on retakes for purchased tests
      navigation.navigate('ExamQuestionsScreen', {
        subject: exam.name,
        examId: exam.id,
        isRetake: true,
        category: exam.category,
        isFullMock: exam.isFullMock
      });
    } else {
      // Navigate to test instructions/payment for unsubscribed exams
      navigation.navigate('TestInstructions', {
        exam: exam
      });
    }
  };
  */

  const renderCategories = () => {
    console.log('Rendering categories, examCategories:', examCategories);
    return (
      <View style={styles.categoriesContainer}>
        <Text style={styles.sectionTitle}>Select an Exam Category</Text>
        <Text style={styles.sectionSubtitle}>Choose a category to view available subjects and tests</Text>

        {loadingCategories ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={COLORS.primary} />
            <Text style={styles.loadingText}>Loading exam categories...</Text>
          </View>
        ) : examCategories.length === 0 ? (
          <View style={styles.emptyStateContainer}>
            <Text style={styles.emptyStateText}>No exam categories available.</Text>
            <TouchableOpacity
              style={styles.retryButton}
              onPress={() => {
                console.log('Retry button pressed');
                loadExamCategories();
              }}
            >
              <Text style={styles.retryButtonText}>Retry</Text>
            </TouchableOpacity>
          </View>
        ) : (
          <View style={styles.categoryGrid}>
            {examCategories.map((category) => (
              <CategoryCard
                key={category.id}
                title={category.name}
                category={category}
                onPress={() => handleCategoryPress(category.id, category.name)}
              />
            ))}
          </View>
        )}
      </View>
    );
  };

  // SubjectCard component for grid layout - improved and uniform
  const SubjectCard = ({ subject, onPress }) => {
    // Get icon based on subject name
    const getIconComponent = () => {
      const iconName = subject.icon || getIconForSubject(subject.name);

      // Determine background color based on subject
      let bgColor = '#F0F0F0';
      let iconColor = '#3461eb';

      if (subject.name.toLowerCase().includes('physics')) {
        bgColor = '#FFE6E6';
        iconColor = '#F44336';
      } else if (subject.name.toLowerCase().includes('chemistry')) {
        bgColor = '#E6F9F0';
        iconColor = '#4CAF50';
      } else if (subject.name.toLowerCase().includes('biology') || subject.name.toLowerCase().includes('botany') || subject.name.toLowerCase().includes('zoology')) {
        bgColor = '#E6F0FF';
        iconColor = '#2196F3';
      } else if (subject.name.toLowerCase().includes('mathematics') || subject.name.toLowerCase().includes('math')) {
        bgColor = '#FFF9E6';
        iconColor = '#FF9500';
      } else if (subject.name.toLowerCase().includes('full mock')) {
        bgColor = '#E6E6FF';
        iconColor = '#673AB7';
      }

      return (
        <View style={[styles.subjectIcon, { backgroundColor: bgColor }]}>
          <MaterialCommunityIcons name={iconName} size={32} color={iconColor} />
        </View>
      );
    };

    // Get duration info for display
    const getDurationInfo = () => {
      const durationMinutes = subject.duration_minutes || 60;
      if (durationMinutes >= 60) {
        const hours = Math.floor(durationMinutes / 60);
        const minutes = durationMinutes % 60;
        return hours + (minutes > 0 ? `h ${minutes}m` : 'h');
      }
      return `${durationMinutes}m`;
    };

    // Get question count info
    const getQuestionCount = () => {
      if (subject.isFullMock) {
        if (selectedCategory?.name === 'NEET') return '180 questions';
        if (selectedCategory?.name === 'JEE') return '90 questions';
        if (selectedCategory?.name === 'GATE') return '65 questions';
        return '100 questions';
      }
      return '50 questions';
    };

    return (
      <TouchableOpacity style={styles.subjectCard} onPress={onPress} activeOpacity={0.7}>
        {getIconComponent()}
        <Text style={styles.subjectTitle} numberOfLines={2}>{subject.name}</Text>
        <Text style={styles.subjectPrice}>{subject.formatted_price || subject.price ? `₹${subject.price}` : 'Free'}</Text>
        <Text style={styles.subjectInfo}>{getQuestionCount()}</Text>
        <View style={styles.subjectMetaContainer}>
          <View style={styles.subjectMeta}>
            <MaterialIcons name="access-time" size={14} color={COLORS.textSecondary} />
            <Text style={styles.subjectMetaText}>{getDurationInfo()}</Text>
          </View>
          {subject.isFullMock && (
            <View style={styles.mockBadge}>
              <Text style={styles.mockBadgeText}>MOCK</Text>
            </View>
          )}
        </View>
      </TouchableOpacity>
    );
  };

  const renderSubjects = () => {
    if (!selectedCategory) return null;

    return (
      <View style={styles.subjectsContainer}>
        <Text style={styles.sectionTitle}>{selectedCategory.name} Subjects</Text>
        <Text style={styles.sectionSubtitle}>Select a subject to take a practice test</Text>

        {loadingSubjects ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={COLORS.primary} />
            <Text style={styles.loadingText}>Loading subjects...</Text>
          </View>
        ) : subjects.length === 0 ? (
          <View style={styles.emptyStateContainer}>
            <MaterialIcons name="info-outline" size={48} color={COLORS.textSecondary} />
            <Text style={styles.emptyStateText}>No subjects available for this category.</Text>
            <TouchableOpacity
              style={styles.retryButton}
              onPress={() => fetchSubjectsForCategory(selectedCategory.id, selectedCategory.name)}
            >
              <Text style={styles.retryButtonText}>Retry</Text>
            </TouchableOpacity>
          </View>
        ) : (
          <View style={styles.subjectGrid}>
            {subjects.map((subject, index) => (
              <SubjectCard
                key={subject.id || index}
                subject={subject}
                onPress={() => handleSubjectPress(subject)}
              />
            ))}
          </View>
        )}
      </View>
    );
  };

  console.log('Rendering FullExamListScreen');

  return (
    <SafeAreaView style={styles.container}>
      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={COLORS.primary} />
          <Text style={styles.loadingText}>Loading exams...</Text>
        </View>
      ) : (
        <Animated.View style={{ flex: 1, opacity: fadeAnim }}>
          {!selectedCategory ? (
            // Show categories
            <View style={{ flex: 1 }}>
              {/* Header is now provided by Tab.Screen options */}
              <ScrollView
                contentContainerStyle={styles.scrollContent}
                showsVerticalScrollIndicator={false}
                refreshControl={
                  <RefreshControl
                    refreshing={refreshing}
                    onRefresh={onRefresh}
                    colors={[COLORS.primary]}
                    tintColor={COLORS.primary}
                  />
                }
              >
                {renderCategories()}
              </ScrollView>
            </View>
          ) : (
            // Show subjects for selected category
            <View style={{ flex: 1 }}>
              <ScrollView
                contentContainerStyle={styles.scrollContent}
                showsVerticalScrollIndicator={false}
              >
                {renderSubjects()}
              </ScrollView>
            </View>
          )}
        </Animated.View>
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: COLORS.textSecondary,
    ...FONTS.medium,
  },
  header: {
    backgroundColor: COLORS.background,
    padding: 16,
    paddingBottom: 8,
    flexDirection: 'row',
    justifyContent: 'space-between',
    borderBottomWidth: 1,
    borderBottomColor: COLORS.border,
  },
  headerTitle: {
    color: COLORS.text,
    fontSize: 18,
    ...FONTS.bold,
    flex: 1,
    textAlign: 'center',
    letterSpacing: -0.4,
  },
  categoriesContainer: {
    paddingHorizontal: 16,
    paddingTop: 16,
    paddingBottom: 16,
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 20,
  },
  headerWithBack: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  backButton: {
    padding: 8,
    marginBottom: 16,
    borderRadius: 20,
    alignSelf: 'flex-start',
  },
  // Grid layout styles
  categoryGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginTop: 8,
  },
  categoryCard: {
    width: '48%',
    backgroundColor: COLORS.card,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
    alignItems: 'center',
  },
  categoryIcon: {
    width: 64,
    height: 64,
    borderRadius: 32,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
  },
  categoryTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.text,
    marginBottom: 4,
    textAlign: 'center',
  },
  categoryPrice: {
    fontSize: 14,
    fontWeight: 'bold',
    color: COLORS.primary,
    marginBottom: 4,
    textAlign: 'center',
  },
  categorySubjects: {
    fontSize: 12,
    color: COLORS.textSecondary,
    marginBottom: 8,
    textAlign: 'center',
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  ratingText: {
    fontSize: 12,
    fontWeight: '500',
    color: COLORS.text,
    marginLeft: 4,
  },
  reviewsText: {
    fontSize: 12,
    color: COLORS.textSecondary,
    marginLeft: 4,
  },
  // Old category styles (keeping for reference)
  categoryContent: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
  },
  categoryIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 8,
    backgroundColor: COLORS.border,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  categoryDetails: {
    flex: 1,
  },
  categoryName: {
    fontSize: 16,
    color: COLORS.text,
    fontWeight: '500',
    marginBottom: 4,
  },
  categoryDescription: {
    fontSize: 14,
    color: COLORS.textSecondary,
  },
  sectionTitle: {
    fontSize: 22,
    color: COLORS.text,
    ...FONTS.bold,
    marginBottom: 4,
  },
  sectionSubtitle: {
    fontSize: 14,
    color: COLORS.textSecondary,
    ...FONTS.regular,
    marginBottom: 16,
  },
  listContainer: {
    paddingBottom: 20,
  },
  examCard: {
    backgroundColor: COLORS.card,
    borderRadius: 12,
    marginHorizontal: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    overflow: 'hidden',
  },
  examCardContent: {
    flexDirection: 'row',
    padding: 16,
  },
  examIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 8,
    backgroundColor: COLORS.border,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  examDetails: {
    flex: 1,
    justifyContent: 'center',
  },
  examName: {
    fontSize: 16,
    color: COLORS.text,
    ...FONTS.medium,
    marginBottom: 4,
  },
  examDescription: {
    fontSize: 14,
    color: COLORS.textSecondary,
    ...FONTS.regular,
    marginBottom: 8,
  },
  examMetaContainer: {
    flexDirection: 'row',
    marginBottom: 4,
  },
  examMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 16,
  },
  examMetaText: {
    fontSize: 12,
    color: COLORS.textSecondary,
    ...FONTS.regular,
    marginLeft: 4,
  },
  scoreText: {
    fontSize: 13,
    color: COLORS.primary,
    ...FONTS.medium,
    marginTop: 4,
  },
  examActionContainer: {
    justifyContent: 'center',
    alignItems: 'flex-end',
    paddingLeft: 8,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    backgroundColor: COLORS.success,
  },
  statusBadgeText: {
    fontSize: 12,
    color: '#FFFFFF',
    ...FONTS.medium,
  },
  priceBadge: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 12,
    backgroundColor: COLORS.border,
  },
  priceText: {
    fontSize: 14,
    color: COLORS.text,
    ...FONTS.bold,
  },
  emptyStateText: {
    fontSize: 16,
    color: COLORS.textSecondary,
    textAlign: 'center',
    marginBottom: 16,
  },
  retryButton: {
    backgroundColor: COLORS.primary,
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 8,
    marginTop: 8,
  },
  retryButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '500',
  },
  subjectsContainer: {
    flex: 1,
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
  subjectsHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.border,
  },
  subjectsTitle: {
    fontSize: 18,
    ...FONTS.bold,
    color: COLORS.text,
    flex: 1,
    textAlign: 'center',
    letterSpacing: -0.4,
  },
  placeholder: {
    width: 24,
  },
  // We're now using the categoryCard styles for subjects too
  subjectButtonContainer: {
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  subjectButton: {
    backgroundColor: COLORS.primary,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 12,
  },
  subjectButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 8,
  },
  // New styles for improved subject cards
  headerBackButton: {
    marginLeft: 10,
    padding: 8,
  },
  subjectGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginTop: 8,
  },
  subjectCard: {
    width: '48%',
    backgroundColor: COLORS.card,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
    alignItems: 'center',
  },
  subjectIcon: {
    width: 64,
    height: 64,
    borderRadius: 32,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
  },
  subjectTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.text,
    marginBottom: 4,
    textAlign: 'center',
    minHeight: 40, // Ensure consistent height for 2 lines
  },
  subjectPrice: {
    fontSize: 14,
    fontWeight: 'bold',
    color: COLORS.primary,
    marginBottom: 4,
    textAlign: 'center',
  },
  subjectInfo: {
    fontSize: 13,
    color: COLORS.textSecondary,
    marginBottom: 8,
    textAlign: 'center',
  },
  subjectMetaContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    width: '100%',
  },
  subjectMeta: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  subjectMetaText: {
    fontSize: 12,
    color: COLORS.textSecondary,
    marginLeft: 4,
  },
  mockBadge: {
    backgroundColor: COLORS.primary,
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 8,
  },
  mockBadgeText: {
    fontSize: 10,
    color: '#fff',
    fontWeight: 'bold',
  },
  // Improved empty state styles
  emptyStateContainer: {
    padding: 32,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: COLORS.card,
    borderRadius: 12,
    marginTop: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
});

export default FullExamListScreen;