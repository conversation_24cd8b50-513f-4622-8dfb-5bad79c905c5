from flask import request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from app import db
from app.api import bp
from app.models.exam import ExamCategory, Subject
from app.models.user import User

@bp.route('/subjects', methods=['GET'])
def get_subjects():
    """Get all subjects, optionally filtered by category"""
    category_id = request.args.get('category_id', type=int)

    if category_id:
        subjects = Subject.query.filter_by(exam_category_id=category_id).all()
    else:
        subjects = Subject.query.all()

    return jsonify([subject.to_dict() for subject in subjects])

@bp.route('/subjects/category/<int:category_id>', methods=['GET'])
def get_subjects_by_category(category_id):
    """Get all subjects for a specific category"""
    subjects = Subject.query.filter_by(exam_category_id=category_id).all()

    if not subjects:
        # Check if the category exists
        category = ExamCategory.query.get(category_id)
        if not category:
            return jsonify({'error': 'Category not found'}), 404

    return jsonify([subject.to_dict() for subject in subjects])

@bp.route('/subjects/<int:id>', methods=['GET'])
def get_subject(id):
    """Get a specific subject by ID"""
    subject = Subject.query.get_or_404(id)
    return jsonify(subject.to_dict())

@bp.route('/subjects', methods=['POST'])
@jwt_required()
def create_subject():
    """Create a new subject (admin only)"""
    # Check if user is admin
    current_user_id = get_jwt_identity()
    user = User.query.get(current_user_id)

    if not user or not user.is_admin():
        return jsonify({'error': 'Admin privileges required'}), 403

    data = request.get_json() or {}

    required_fields = ['name', 'category_id']
    for field in required_fields:
        if field not in data:
            return jsonify({'error': f'{field} is required'}), 400

    # Check if category exists
    category = ExamCategory.query.get(data['category_id'])
    if not category:
        return jsonify({'error': 'Category not found'}), 404

    # Check if subject already exists in this category
    if Subject.query.filter_by(subject_name=data['name'], exam_category_id=data['category_id']).first():
        return jsonify({'error': 'Subject already exists in this category'}), 400

    subject = Subject(
        subject_name=data['name'],
        description=data.get('description', ''),
        max_retakes=data.get('max_retakes', 3),
        exam_category_id=data['category_id'],
        price=data.get('price', 0.00),
        currency=data.get('currency', 'INR')
    )

    db.session.add(subject)
    db.session.commit()

    return jsonify(subject.to_dict()), 201

@bp.route('/subjects/<int:id>', methods=['PUT'])
@jwt_required()
def update_subject(id):
    """Update a subject (admin only)"""
    # Check if user is admin
    current_user_id = get_jwt_identity()
    user = User.query.get(current_user_id)

    if not user or not user.is_admin():
        return jsonify({'error': 'Admin privileges required'}), 403

    subject = Subject.query.get_or_404(id)
    data = request.get_json() or {}

    if 'name' in data and 'category_id' in data:
        # Check if name is being changed and if it already exists in the category
        if (data['name'] != subject.subject_name or data['category_id'] != subject.exam_category_id) and \
           Subject.query.filter_by(subject_name=data['name'], exam_category_id=data['category_id']).first():
            return jsonify({'error': 'Subject already exists in this category'}), 400

    if 'name' in data:
        subject.subject_name = data['name']

    if 'description' in data:
        subject.description = data['description']

    if 'max_retakes' in data:
        subject.max_retakes = data['max_retakes']

    if 'category_id' in data:
        # Check if category exists
        category = ExamCategory.query.get(data['category_id'])
        if not category:
            return jsonify({'error': 'Category not found'}), 404
        subject.exam_category_id = data['category_id']

    if 'price' in data:
        subject.price = data['price']

    if 'currency' in data:
        subject.currency = data['currency']

    db.session.commit()

    return jsonify(subject.to_dict())

@bp.route('/subjects/<int:id>', methods=['DELETE'])
@jwt_required()
def delete_subject(id):
    """Delete a subject (admin only)"""
    # Check if user is admin
    current_user_id = get_jwt_identity()
    user = User.query.get(current_user_id)

    if not user or not user.is_admin():
        return jsonify({'error': 'Admin privileges required'}), 403

    subject = Subject.query.get_or_404(id)

    # Check if subject has questions
    if subject.questions.count() > 0:
        return jsonify({'error': 'Cannot delete subject with questions'}), 400

    db.session.delete(subject)
    db.session.commit()

    return jsonify({'message': 'Subject deleted successfully'})
